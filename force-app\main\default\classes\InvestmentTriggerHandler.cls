public class InvestmentTriggerHandler
{
    public static Boolean isPointTransactionCreationAllowed = true;
    Map<Id,List<Investment__c>> accInvMap;
    Set<Id> contInvSet;
    String JOINTSOUTSIDESTR = 'Joint A/c - Outside';
    String JOINTSSTR = 'Joint A/c';
    //Added by <PERSON><PERSON> for Balance for Sale with IPV (Direct) 30/06/2022.
    list<Startup_Round__c> strtupList = new list<Startup_Round__c>();
    set<id> StrtupInvIDs = new set<id>();
    // Added by <PERSON>kush for Deal Tracker 25/08/2022.
    Set<id> StrtupRoundIds = new set<id>();
    Map<Id,startup_round__c> StrRoundRecords = new Map<Id,startup_round__c>();
    
    
    public void beforeInsert(List<Investment__c> invList)
    {

        List<Contact> contactInsertList = new List<Contact>();
        Map<String,Contact> contactMap = new Map<String,Contact>();
        Map<id,Map<String,Contact>> accContactTypeMap = new Map<id,Map<String,Contact>>();
        Map<id,Account> accountMap = new Map<id,Account>();
        Map<String,Startup_Round__c> startupRoundMap = new Map<String,Startup_Round__c>();
        Map<Id , Id> parentInvestmentId = new Map<Id , Id>();
        String invPanToUppercase;
        Map<String,List<Investment__c>> jointInvMap = new Map<String,List<Investment__c>>();
        Map<String,Integer> priJointStartUpMap = new Map<String,Integer>();

        //Map<Id,Set<String>> accBOACountNewMap = new Map<Id,Set<String>>();

        // added by Sahilparvat on 04.08.2025 for PAN Validation
        // Validate PAN numbers before processing
        validateInvestorPAN(invList);

        //Added by Karan to update checkboxs for Balance of sale 30/06/2022.
        for(Investment__c inv : invList){

            if(inv.Parent_Investment__c != null)
            {
                parentInvestmentId.put(inv.Id , inv.Parent_Investment__c);    
            }

            System.debug('Internal Transfer>>>>>>>'+inv.Intransfer_balance_share__c);
            if((inv.Investor__c == System.Label.Investor_IPV_Advisors_Pvt_Ltd && inv.Issue_Type__c == 'Primary') && (inv.Type__c == 'Internal Transfers' && inv.Investor_Type__c =='Via Platform')){
                inv.Intransfer_balance_share__c = true;
            }else{
                inv.Intransfer_balance_share__c = false;
            }
            System.debug('Internal Transfer22>>>>>>>'+inv.Intransfer_balance_share__c);
            System.debug('Ivested number of shares>>>>>>>'+inv.Invested_number_of_share__c);
            if((inv.Investor__c == System.Label.Investor_IPV_Advisors_Pvt_Ltd && inv.Issue_Type__c == 'Primary') && (inv.Type__c == 'Invested' || inv.Type__c == 'Committed') && inv.Investor_Type__c =='Via Platform'){
                inv.Invested_number_of_share__c = true;
            }else{
                inv.Invested_number_of_share__c = false;
            }
            System.debug('Ivested number of shares22>>>>>>>'+inv.Invested_number_of_share__c);
            if(inv.Issue_Type__c == 'IPV HQ - Shadow' && inv.Type__c == 'Invested - Shadow' && inv.Investor_Type__c =='Via Platform'){
                inv.IPVHQ_Shadow_Balance_Share__c = true;
            }else{
                inv.IPVHQ_Shadow_Balance_Share__c = false;
            }
            System.debug('IPVHQ Shadow>>>>>>>'+inv.IPVHQ_Shadow_Balance_Share__c);
            if(inv.Issue_Type__c == 'IPV FnF - Shadow' && inv.Type__c == 'Invested - Shadow' && inv.Investor_Type__c =='Via Platform'){
                inv.IPVFnF_Shadow_Balance_share__c = true;
            }else{
                inv.IPVFnF_Shadow_Balance_share__c = false;
            }
            if((inv.Issue_Type__c == 'Friends & Family T1' || inv.Issue_Type__c == 'Friends & Family T2') && (inv.Type__c == 'Invested' || inv.Type__c == 'Committed') && inv.Investor_Type__c =='Via Platform'){
                inv.Family_Balance_Share__c = true;
            }else{
                inv.Family_Balance_Share__c = false;
            }
            if(inv.IRR_Percentage_Formula__c != null){
                inv.Copy_Of_IRR_Unrealised__c = inv.IRR_Percentage_Formula__c;
            }
            
           if(inv.Sent_Email_Communications__c == null){
                inv.Sent_Email_Communications__c = 'None';
            }
            
            if(inv.Internal_transfer_check__c != null){
                inv.Exit_and_Internal_check_copy__c = inv.Internal_transfer_check__c;
            }
           
        }

        Map<Id , Investment__c> parentInvestmentMap = new Map<Id , Investment__c>([SELECT Id  , Number_Of_Shares__c FROM Investment__c WHERE Id IN :parentInvestmentId.values()]);
        
        for(Investment__c inv : invList)
        {

            //  Added By Sahilparvat To Calculate The "Exit Type" On 21.06.2024
            if(inv.Parent_Investment__c != null)
            {
                Investment__c parentInvestment = parentInvestmentMap.get(parentInvestmentId.get(inv.Id));
                if((parentInvestment.Number_Of_Shares__c != null && inv.Number_Of_Shares__c != null) && parentInvestment.Number_Of_Shares__c == inv.Number_Of_Shares__c && inv.Type__c == 'Exit')
                {
                    inv.Exit_Type__c = 'Full Exit';
                }
                
                if((parentInvestment.Number_Of_Shares__c != null && inv.Number_Of_Shares__c != null) && parentInvestment.Number_Of_Shares__c != inv.Number_Of_Shares__c && inv.IRR_Value__c > 0 && inv.Type__c == 'Exit')
                {
                    inv.Exit_Type__c = 'Partial Exit';
                }
            }
            
            if(inv.IRR_Value__c == 0)
            {
                inv.Exit_Type__c = 'Capital Return';
            }
               
            if(inv.IRR_Value__c < 0)
            {
                inv.Exit_Type__c = 'Liquidation';
            }

            system.debug('Startup_Round__r.Startup__c>>>>>>>>'+inv.Startup_Round__r.Startup__c);
            system.debug('inv.Investor_s_PAN__c>>>>>>>>'+inv.Investor_s_PAN__c);
            invPanToUppercase=inv.Investor_s_PAN__C;
            
            if(invPanToUppercase!=null && invPanToUppercase!='')
            {
                invPanToUppercase = invPanToUppercase.trim();
                invPanToUppercase = invPanToUppercase.replaceAll('\\s+', '');
                invPanToUppercase = invPanToUppercase.toUppercase();
            }
            system.debug('invPanToUppercase>>>>>>>>'+invPanToUppercase);
            
            if(inv.Startup_Round__c==null)
            {
                if(inv.Startup_Round_Name__c!=null && inv.Startup_Round_Name__c!='')
                    startupRoundMap.put(inv.Startup_Round_Name__c,null);
                else
                    inv.addError('Startup Round does not found!');
            }        
            if(invPanToUppercase !=null && invPanToUppercase !='')
                contactMap.put(invPanToUppercase,null);
            else if(/*inv.Type__c == 'Invested' && */ !inv.Is_Investor_Exists__c && inv.Type__c !='Incomplete Data')
                inv.addError('Investor\'s PAN is required field.');
            
            if(inv.Account__c!=null)
                accountMap.put(inv.Account__c,null);
            else
                inv.addError('Account is required field.');
            
            //System.debug('INV ****************'+inv);
            //Joints Scenarios
            if(inv.Investment_in_Own_Name_Family_Member__c==JOINTSOUTSIDESTR)
            {
                if(!inv.Is_Primary__c)
                {
                    if(inv.Primary_Holder_Contact__c!=null && inv.Primary_Holder_Contact__c!='')
                    {
                        if(!jointInvMap.containsKey(inv.Primary_Holder_Contact__c))
                            jointInvMap.put(inv.Primary_Holder_Contact__c,new List<Investment__c>());
                        
                        jointInvMap.get(inv.Primary_Holder_Contact__c).add(inv);
                    }
                    else
                    {
                        inv.addError('Primary Holder Contact is required for Joints A/C - Outside');
                    }
                }
                else
                {
                    if(!priJointStartUpMap.containsKey(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c))
                        priJointStartUpMap.put(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c,1);
                    else
                        priJointStartUpMap.put(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c,priJointStartUpMap.get(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c)+1);
                }
            }
            
        }
        system.debug('priJointStartUpMap>>>>>>>>'+priJointStartUpMap);
        
        if(accountMap!=null && accountMap.size()>0)
        {            
            for(Account acc : [select id,name,Get_Physical_Certificate__c,Primary_Contact__c,Official_Email__c,Personal_Email__c,(select id,Email,Postal_Address__c,Investor_Father_Husband_s_name__c ,Residential_Status__c ,Aadhar_Card__c,LastName,Investor_s_PAN__c,Investment_in_Own_Name_Family_Member__c from contacts) from Account where id in : accountMap.keyset()])
            {
                accountMap.put(acc.id,acc);
                
                for(contact con: acc.contacts){
                    system.debug('###'+con.LastName);
                    system.debug('###'+con.Investment_in_Own_Name_Family_Member__c );
                    
                    if(!accContactTypeMap.containsKey(acc.Id))
                    {
                        accContactTypeMap.put(acc.Id,new Map<String,contact>());
                    }
                    
                    if(con.Investment_in_Own_Name_Family_Member__c!=null)
                        accContactTypeMap.get(acc.ID).put(con.Investment_in_Own_Name_Family_Member__c,con);                   
                }
                
            }
            
            //Show the name of all the startups separated by commas wherever type is "Back out unapproved"  || Date: 30-05-2021     
            //accBOACountNewMap = getBackOutUnapproved(accountMap.keyset());
            
        }
        
        system.debug('accContactTypeMap>>>>'+accContactTypeMap);
        
        if(startupRoundMap!=null && startupRoundMap.size()>0)
        {            
            for(Startup_Round__c sr : [select id,name,Date_of_sending_out_call_for_money__c from Startup_Round__c where name in : startupRoundMap.keyset()])
            {
                startupRoundMap.put(sr.name,sr);
            }
        }
        
        if(contactMap!=null && contactMap.size()>0)
        {
            for(Contact con : [select id,AccountId,Investor_s_PAN__c from contact where Investor_s_PAN__c in : contactMap.Keyset()])
            {
                system.debug('con.Investor_s_PAN__c>>>>>>>>'+con.Investor_s_PAN__c);
                contactMap.put(con.Investor_s_PAN__c ,con);
            }
        }
        
        //Joints Scenario
        if(jointInvMap!=null && jointInvMap.size()>0)
        {
            System.debug('jointInvMap ****************'+jointInvMap);
            List<Investment__c> existingInvList = [select id,Account__r.Primary_Contact__c,Startup_Round__c,Startup_Round_Name__c,Is_Primary__c,Primary_Holder_Contact__c from Investment__c where Account__r.Primary_Contact__c in : jointInvMap.Keyset() AND Investment_in_Own_Name_Family_Member__c=:JOINTSOUTSIDESTR];            
            System.debug('INV ****************'+existingInvList );
            
            for(Investment__c invEx : existingInvList)
            {
                if(invEx.Is_Primary__c)
                {
                    if(!priJointStartUpMap.containsKey(invEx.Primary_Holder_Contact__c+'-'+invEx.Startup_Round__c))
                        priJointStartUpMap.put(invEx.Primary_Holder_Contact__c+'-'+invEx.Startup_Round__c,1);
                    else
                        priJointStartUpMap.put(invEx.Primary_Holder_Contact__c+'-'+invEx.Startup_Round__c,priJointStartUpMap.get(invEx.Primary_Holder_Contact__c+'-'+invEx.Startup_Round__c)+1);
                }
            }
        }
        System.debug('priJointStartUpMap ****************'+priJointStartUpMap);
        
        for(Investment__c inv : invList)
        {
            //if(inv.Type__c == 'Invested'){
            
            system.debug('accountMap>>>>>'+accountMap);
            system.debug('contactMap>>>>>'+contactMap);
            system.debug('startupRoundMap>>>>>'+startupRoundMap);
            system.debug('contactMap.get(inv.Investor_s_PAN__c)>>>>>'+contactMap.get(inv.Investor_s_PAN__c));
            
            system.debug('1inv.Investment_in_Own_Name_Family_Member__c>>>>>>>>'+inv.Investment_in_Own_Name_Family_Member__c);
            system.debug('1inv.Investor_s_PAN__c>>>>>>>>'+inv.Investor_s_PAN__c);
            invPanToUppercase=inv.Investor_s_PAN__C;
            
            system.debug('String.isBlank(invPanToUppercase)>>>>'+String.isBlank(invPanToUppercase));
            
            if(invPanToUppercase!=null && invPanToUppercase!='')
            {
                invPanToUppercase = invPanToUppercase.trim();
                invPanToUppercase = invPanToUppercase.replaceAll('\\s+', '');
                invPanToUppercase = invPanToUppercase.toUppercase();
            }            
            system.debug('1invPanToUppercase>>>>>>>>'+invPanToUppercase);
            
            if(accountMap!=null && accountMap.size()>0 && accountMap.get(inv.account__c)!=null)
            {
                inv.Phone_No__c = accountMap.get(inv.account__c).Primary_Contact__c;
                inv.Get_Physical_Certificate__c = accountMap.get(inv.account__c).Get_Physical_Certificate__c;   
                inv.Official_Email__c = accountMap.get(inv.account__c).Official_Email__c;                  
                inv.Personal_Email__c = accountMap.get(inv.account__c).Personal_Email__c;                  
            }
            //Joints Scenario
            if(inv.Investment_in_Own_Name_Family_Member__c==JOINTSOUTSIDESTR)
            {
                if(inv.Primary_Holder_Contact__c!=null && inv.Primary_Holder_Contact__c!='')
                {
                    
                    system.debug('holders>>>>>>>>>>>>'+inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c);
                    system.debug('holders>>>>>>>>>>>>'+priJointStartUpMap.get(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c));
                    
                    if(!priJointStartUpMap.containsKey(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c))
                        inv.addError('Primary Holder Account Not Found.');
                    else if(priJointStartUpMap.containsKey(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c) && priJointStartUpMap.get(inv.Primary_Holder_Contact__c+'-'+inv.Startup_Round__c)>1)
                    {    
                        //temporary removed it (18/07/23) inv.addError('More Than One Primary Account Found For The Same Startup Round.');
                        system.debug('ERRORSSSSSS>>>>>>>>>>>>'+inv);
                        system.debug('ERRORSSSSSS Type__c>>>>>>>>>>>>'+inv.Type__c);
                        system.debug('ERRORSSSSSS Startup_Round__c>>>>>>>>>>>>'+inv.Startup_Round__c);
                    }
                }
                else
                {
                    inv.addError('Primary Holder Contact is required for Joints A/C - Outside');
                }
            }
            system.debug('inv.Is_Investor_Exists__c>>>>>>>>'+inv.Is_Investor_Exists__c);
            
            if(inv.Is_Investor_Exists__c)
            {
                system.debug('accContactTypeMap.containsKey(inv.account__c)>>>>>'+accContactTypeMap.containsKey(inv.account__c));
                system.debug('inv.account__c>>>>>'+inv.account__c);
                system.debug('inv.Investment_in_Own_Name_Family_Member__c>>>>>'+inv.Investment_in_Own_Name_Family_Member__c);
                
                if(accContactTypeMap.containsKey(inv.account__c))
                {
                    if(inv.Investment_in_Own_Name_Family_Member__c!=null && accContactTypeMap.get(inv.account__c).containsKey(inv.Investment_in_Own_Name_Family_Member__c))
                    {
                        inv.Investor__c = accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).Id;
                        inv.Investor_Name__c = accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).LastName;
                        inv.Investor_s_PAN__c = accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).Investor_s_PAN__c;
                        inv.Email_ID__c = accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).Email;
                        inv.Postal_Address__c = accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).Postal_Address__c;
                        inv.Investor_Father_Husband_s_name__c = accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).Investor_Father_Husband_s_name__c;
                        inv.Residential_Status__c = accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).Residential_Status__c;
                        inv.Aadhar_Card__c= accContactTypeMap.get(inv.account__c).get(inv.Investment_in_Own_Name_Family_Member__c).Aadhar_Card__c;
                    }
                    else
                    {
                        inv.addError('Investor not found for '+inv.Investment_in_Own_Name_Family_Member__c);
                    }
                }
                
            }
            else
            {
                if(contactMap!=null && contactMap.get(invPanToUppercase)!=null)
                {
                    if(contactMap.get(invPanToUppercase).AccountId!=inv.Account__c)
                        inv.addError('Account and Investor are not matching.');
                    else
                        inv.Investor__c = contactMap.get(invPanToUppercase).Id;
                }
                else/* if(inv.Type__c == 'Invested')*/
                {
                    
                    if(invPanToUppercase!=null && invPanToUppercase!='')
                    {
                        system.debug('Insert invPanToUppercase contact>>>>'+invPanToUppercase);
                        Contact con = new Contact();
                        con.Investment_in_Own_Name_Family_Member__c = inv.Investment_in_Own_Name_Family_Member__c;
                        con.AccountId = inv.Account__c;
                        con.Residential_Status__c = inv.Residential_Status__c;
                        con.email = inv.Email_ID__c;
                        con.Investor_Father_Husband_s_name__c = inv.Investor_Father_Husband_s_name__c;
                        con.Aadhar_Card__c = inv.Aadhar_Card__c;
                        con.LastName = inv.Investor_Name__c;
                        con.Phone = inv.Phone_No__c;
                        con.Postal_Address__c = inv.Postal_Address__c;
                        con.Investor_s_PAN__c = invPanToUppercase;
                        
                        contactMap.put(con.Investor_s_PAN__c ,con);
                        //inv.Investor__c = con.Id;
                        contactInsertList.add(con);
                    }
                    else if(inv.Type__c !='Incomplete Data')
                    {
                        inv.addError('Investor\'s PAN is required field.');
                    }
                }
            }
            
            
            //}  
            
            if(inv.Startup_Round_Name__c!=null && startupRoundMap!=null && startupRoundMap.get(inv.Startup_Round_Name__c)!=null)
            {
                inv.Startup_Round__c = startupRoundMap.get(inv.Startup_Round_Name__c).Id;
                
                //For report we are populating Investment_Year__c to create roll up sumrry field on account
                if(startupRoundMap.get(inv.Startup_Round_Name__c).Date_of_sending_out_call_for_money__c!=null)
                {
                    Date invDate = startupRoundMap.get(inv.Startup_Round_Name__c).Date_of_sending_out_call_for_money__c;
                    inv.Investment_Year__c = ''+invDate.year();
                }
            }
            else if(inv.Startup_Round__c==null)
                inv.addError('Startup Round does not found!');
            
            /*     
//Show the name of all the startups separated by commas wherever type is "Back out unapproved"  || Date: 30-05-2021     
if(accBOACountNewMap!=null && accBOACountNewMap.containsKey(inv.Account__c) && accBOACountNewMap.get(inv.Account__c).size()>0)
{
List<String> tempStartUpList = new List<String>();
tempStartUpList.addAll(accBOACountNewMap.get(inv.Account__c));
inv.Total_Back_Outs_Unapproved__c = string.join(tempStartUpList,',');//accBOACountNewMap.get(inv.Account__c).toString();
}
*/
        }
        
        system.debug('contactInsertList>>>>>'+contactInsertList);
        system.debug('contactInsertList size>>>>>'+contactInsertList.size());
        if(contactInsertList!=null && contactInsertList.size()>0)
        {
            //insert contactInsertList;
            Database.SaveResult[] srList = Database.insert(contactInsertList, false);
            Map<String,String> errorPANMap = new Map<String,String>();
            
            for(Integer i=0;i<srList.size();i++){
                
                //for (Database.SaveResult sr : srList)
                if (srList[i].isSuccess()){
                    System.debug('Successfully inserted contact. Contact ID: ' + srList[i].getId()+'<<For PAN>>>'+contactInsertList[i].Investor_s_PAN__c);
                    
                } 
                else{
                    system.debug(srList[i].getErrors());
                    system.debug(contactInsertList[i]); 
                    
                    String errorMsg='';
                    for(Database.Error err : srList[i].getErrors()){
                        System.debug('InvestmentTriggerHandler The following error has occurred.');                    
                        System.debug(err.getStatusCode() + ': ' + err.getMessage());
                        
                        errorMsg = 'ERROR: '+err.getMessage()+'. \n'+errorMsg ;
                    }
                    
                    invPanToUppercase = contactInsertList[i].Investor_s_PAN__c;
                    if(invPanToUppercase!=null && invPanToUppercase!='' && contactInsertList[i].Residential_Status__c != 'Foreign National')
                    {
                        invPanToUppercase = invPanToUppercase.toUppercase();
                        //errorPANMap.put(invPanToUppercase,''+srList[i].getErrors().getMessage());
                        errorPANMap.put(invPanToUppercase,errorMsg);
                    }
                }
            }
            
            system.debug('errorPANMap>>>>>>>>'+errorPANMap);
            
            for(Investment__c inv : invList)
            {
                system.debug('2inv.Investor_s_PAN__c>>>>>>>>'+inv.Investor_s_PAN__c);
                invPanToUppercase=inv.Investor_s_PAN__C;
                
                if(invPanToUppercase!=null && invPanToUppercase!='')
                    invPanToUppercase = invPanToUppercase.toUppercase();
                
                system.debug('contactMap>>>>>>>>'+contactMap);
                system.debug('2invPanToUppercase>>>>>>>>'+invPanToUppercase);
                
                //if(inv.Type__c == 'Invested'){
                
                if(contactMap!=null && contactMap.get(invPanToUppercase)!=null && contactMap.get(invPanToUppercase).AccountId==inv.Account__c)
                {
                    if(errorPANMap!=null && errorPANMap.containskey(invPanToUppercase))
                        inv.addError(errorPANMap.get(invPanToUppercase));
                    else
                        inv.Investor__c = contactMap.get(invPanToUppercase).Id;
                }
                //}
            }
        }    
        
        calculateIRR(invList,null);

    }
    
    public void beforeUpdate(List<Investment__c> invList,Map<id,Investment__c> oldInvMap)
    {
        if (pointTransactionHelper.hasExecuted) {
            System.debug('createPointTransaction already executed. Skipping...');
            return;
        }
        pointTransactionHelper.hasExecuted = true;

        // added by Sahilparvat on 04.08.2025 for PAN Validation
        // Validate PAN numbers before processing
        validateInvestorPAN(invList);     

        List<Investment__c> exitInvList = new List<Investment__c >();
        Map<id,Account> accountMap = new Map<id,Account>();
        Set<id> InvestoridSet = new set<Id>();
        Map<Id , Id> parentInvestmentId = new Map<Id , Id>();
        
        /*Added by Karan for Bulkify autocom Switch 26/05/2023.
        
        Set<Id> investmentIds = new Set<Id>();
        Map<id,investment__c> InvSwitchMap = new Map<id,Investment__C>();
        Map<id,investment__c> RoundSwitchMap = new Map<id,investment__c>();
        for (Investment__c inv : invList) {
            investmentIds.add(inv.Id);
        }
        Map<Id, Investment__c> autoComSwitch = new Map<Id, Investment__c>([SELECT Id, Investor__r.Send_Auto_Com_Email__c, Startup_Round__r.Send_Auto_Comms_Email__c FROM Investment__c WHERE Id IN :investmentIds]);
        for (Investment__c inv : invList) {
            if (autoComSwitch.containsKey(inv.Id)) {
                Investment__c invt = autoComSwitch.get(inv.Id);
                if (invt.Investor__r.Send_Auto_Com_Email__c != null) {
                    invSwitchMap.put(invt.Id, invt);
                    // InvestorSwitch = invt.Investor__r.Send_Auto_Com_Email__c;
                }
                if (invt.Startup_Round__r.Send_Auto_Comms_Email__c != null) {
                    roundSwitchMap.put(invt.Id, invt);
                    // RoundSwitch = invt.Startup_Round__r.Send_Auto_Comms_Email__c;
                }
            }
        }
       
            system.debug('SwitchMap234>>'+InvSwitchMap);
            List<String> Multipicklist = new list<string>();
        // Added by ankush for mapping of Commuication on Investment. 1.3.23.
            System.debug('InvList419>>>>>>>' +invList);
            for(Investment__c inv : invList)
            {
                if(Inv.Sent_Email_Communications__c != null)
                Multipicklist = Inv.Sent_Email_Communications__c.split(';');
                if(Multipicklist !=null && Multipicklist.size() > 10 ){
                    Inv.addError('Can not capture more than 10 communications');
                }
                If(Inv.Investor__c != null && OldInvMap.get(Inv.id).Investor__C != Inv.Investor__c){
                    InvestoridSet.add(Inv.Investor__c);
                    }
                if(!RecursiveHandler.invIdsEmailNotification.contains(inv.Id))
                    {
                        if(inv.Sent_Email_Communications__c == null || Inv.Sent_Email_Communications__c =='')
                        inv.Sent_Email_Communications__c ='None';
                
                        if(Inv.IPV_Fees_Cheque__c =='Yes' &&  InvSwitchMap.get(inv.id).Investor__r.Send_Auto_Com_Email__c==true && RoundSwitchMap.get(inv.id).Startup_Round__r.Send_Auto_Comms_Email__c == true && OldInvMap.get(Inv.id).IPV_Fees_Cheque__c != Inv.IPV_Fees_Cheque__c && Inv.Issue_Type__c !='Friends & Family T2'
                            && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='IPV HQ - Shadow' || inv.Issue_Type__c=='IPV FnF - Shadow' || inv.Issue_Type__c==' IPV Employee')){
                                Inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'IPV Investment Fee received confirmation.';
                            }
                    if(Inv.Sent_Email_Communications__c != null && InvSwitchMap.get(Inv.id).Investor__r.Send_Auto_Com_Email__c == true && RoundSwitchMap.get(inv.id).Startup_Round__r.Send_Auto_Comms_Email__c == true && oldInvMap.get(Inv.id).Type__C != Inv.Type__c){
                        
                        if(inv.Type__c=='Back out approved' && inv.Type__c!=oldInvMap.get(inv.Id).Type__c )
                            inv.Sent_Email_Communications__c =Inv.Sent_Email_Communications__c +';'+'Back out approved confirmation'; 
                 
                        if (inv.Type__c=='Back out unapproved' && inv.Type__c!=oldInvMap.get(inv.Id).Type__c )
                            inv.Sent_Email_Communications__c =Inv.Sent_Email_Communications__c +';'+'Back out unapproved confirmation';
                 
                        if (inv.Type__c=='Round Closed - Commitment Released')
                            inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';'+'Commitment Released confirmation';
                 
                        if (Inv.Type__c == 'Round closed - deal dropped' && OldInvMap.get(inv.id).Type__c != Inv.Type__c){
                            Inv.Sent_Email_Communications__c =Inv.Sent_Email_Communications__c +';' +'Commitment Released confirmation - Due to deal dropped.';
                            }
                
                    
                     if(Inv.Type__c == 'Waitlist' && oldInvMap.get(Inv.id).type__c != Inv.Type__c){
                            if(inv.Reason_for_waitlist__c=='Over Subscription')
                                inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'For waitlist members - Over Subscription';
            
                            else if(inv.Reason_for_waitlist__c=='Unapproved Back Outs')
                                inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'For waitlist members - Unapproved Backout';
            
                            else if(inv.Reason_for_waitlist__c =='Pending Documents')
                                Inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'For waitlist members - Pending Documents';
            
                            else if(Inv.Reason_for_waitlist__c == 'IPV Fee Pending')
                                Inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'For waitlist members - IPV fee Pending';
                            }
            
                    if(inv.Type__c=='Invested' && inv.Type__c != oldInvMap.get(inv.Id).Type__c){
                            if(Inv.Issue_Type__c =='Primary')
                                Inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'Platform - Investment Amount received confirmation';
                
                            else if((inv.Issue_Type__c =='Primary - AIF' || inv.Issue_Type__c =='Friends & Family T1 - AIF' || inv.Issue_Type__c =='Friends & Family T2 - AIF'))
                                Inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'AIF -Investment Amount received confirmation';
                
                            else if(inv.Issue_Type__c == 'Friends & Family T2' && inv.IPV_Fees_Cheque__c=='Yes')
                                Inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c +';' +'Fnf T2 - Investment Amount Received confirmation';
            
                            else if(Inv.Issue_Type__c =='Friends & Family T1')
                                Inv.Sent_Email_Communications__c = Inv.Sent_Email_Communications__c+';' +'Fnf T1 - Investment Amount Received confirmation';
                            }
                            //System.debug('Comms>>>>>>'+Inv.Sent_Email_Communications__c);
                    
                     if(Inv.Type__c == 'Committed' && oldInvMap.get(Inv.id).Type__C != Inv.Type__c)
                        {
                            if (inv.Membership_status__c=='On Trial' || inv.Membership_status__c=='On Trial Community')
                                    inv.Sent_Email_Communications__c =inv.Sent_Email_Communications__c +';'+'Trial members/ Trial community members who have committed';
                         
                            if(((inv.Membership_status__c=='Paid IPV Fee' || inv.Membership_status__c=='Paid Community' || inv.Membership_status__c=='Complimentary' || inv.Membership_status__c=='Paid by IPV Points' || inv.Membership_status__c=='Paid by CXO Points' || inv.Membership_status__c=='Add On' || inv.Membership_status__c=='IPV Team')
                                && (inv.Pending_days_to_expiry__c < 30 && inv.Membership_Validity__c > date.Today())))
                                    inv.Sent_Email_Communications__c = inv.Sent_Email_Communications__c +';'+'Due in 1 month';
                            
                            if(((inv.Membership_status__c=='Paid IPV Fee' || inv.Membership_status__c=='Paid Community' || inv.Membership_status__c=='Complimentary' || inv.Membership_status__c=='Paid by IPV Points' || inv.Membership_status__c=='Paid by CXO Points' || inv.Membership_status__c=='Add On' || inv.Membership_status__c=='IPV Team')
                                && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Primary - AIF' || inv.Issue_Type__c=='Friends & Family T1 - AIF' || inv.Issue_Type__c=='Friends & Family T2 - AIF' || inv.Issue_Type__c==' IPV Employee')))
                                    inv.Sent_Email_Communications__c = inv.Sent_Email_Communications__c +';'+'Confirmation for waitlist members and normal';
                             
                            if((inv.Residential_Status__c=='NRI' || inv.Residential_Status__c=='OCI Holder')
                                && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Friends & Family T1' || inv.Issue_Type__c=='Friends & Family T2'))
                                    inv.Sent_Email_Communications__c = inv.Sent_Email_Communications__c +';'+'NRI Communication';
                         
                            if(inv.Issue_Type__c == 'Friends & Family T1')
                                    inv.Sent_Email_Communications__c = inv.Sent_Email_Communications__c+';'+'Fnf T1 - Confirmation of committed members';
                         
                            if(inv.Issue_Type__c == 'Friends & Family T2')
                                    inv.Sent_Email_Communications__c = inv.Sent_Email_Communications__c +';'+'Fnf T2 - Confirmation of Committed members';
                            }
                        } 
                     }
                 } */
        
        //Added by Karan to update checkboxs for Balance of sale 30/06/2022.
        for(Investment__c inv : invList){
            
            if(inv.Parent_Investment__c != null)
            {
                parentInvestmentId.put(inv.Id , inv.Parent_Investment__c);    
            }
            
            System.debug('Internal Transfer>>>>>>>'+inv.Intransfer_balance_share__c);
            if((inv.Investor__c == System.Label.Investor_IPV_Advisors_Pvt_Ltd && inv.Issue_Type__c == 'Primary') && (inv.Type__c == 'Internal Transfers') && inv.Investor_Type__c =='Via Platform'){
                inv.Intransfer_balance_share__c = true;
            }else{
                inv.Intransfer_balance_share__c = false;
            }
            System.debug('Internal Transfer22>>>>>>>'+inv.Intransfer_balance_share__c);
            System.debug('Ivested number of shares>>>>>>>'+inv.Invested_number_of_share__c);
            if((inv.Investor__c == System.Label.Investor_IPV_Advisors_Pvt_Ltd && inv.Issue_Type__c == 'Primary') && (inv.Type__c == 'Invested' || inv.Type__c == 'Committed') && inv.Investor_Type__c =='Via Platform'){
                inv.Invested_number_of_share__c = true;
            }else{
                inv.Invested_number_of_share__c = false;
            }
            System.debug('Ivested number of shares22>>>>>>>'+inv.Invested_number_of_share__c);
            if(inv.Issue_Type__c == 'IPV HQ - Shadow' && inv.Type__c == 'Invested - Shadow' && inv.Investor_Type__c =='Via Platform'){
                inv.IPVHQ_Shadow_Balance_Share__c = true;
            }else{
                inv.IPVHQ_Shadow_Balance_Share__c = false;
            }
            System.debug('IPVHQ Shadow>>>>>>>'+inv.IPVHQ_Shadow_Balance_Share__c);
            if(inv.Issue_Type__c == 'IPV FnF - Shadow' && inv.Type__c == 'Invested - Shadow' && inv.Investor_Type__c =='Via Platform'){
                inv.IPVFnF_Shadow_Balance_share__c = true;
            }else{
                inv.IPVFnF_Shadow_Balance_share__c = false;
            }
            if((inv.Issue_Type__c == 'Friends & Family T1' || inv.Issue_Type__c == 'Friends & Family T2') && (inv.Type__c == 'Invested' || inv.Type__c == 'Committed') && inv.Investor_Type__c =='Via Platform'){
                inv.Family_Balance_Share__c = true;
            }else{
                inv.Family_Balance_Share__c = false;
            }
            if(inv.IRR_Percentage_Formula__c != null){
                inv.Copy_Of_IRR_Unrealised__c = inv.IRR_Percentage_Formula__c;
            }
            
            if(inv.Internal_transfer_check__c != null){
                inv.Exit_and_Internal_check_copy__c = inv.Internal_transfer_check__c;
            }
            If(Inv.Investor__c != null && oldInvMap.get(Inv.id).Investor__C != Inv.Investor__c){
                    InvestoridSet.add(Inv.Investor__c);
                   }
           
        }
        
         // Added by Ankush for Investor Info flow.
        Map<id,Contact> InvestorInfoMap = new Map<id,Contact>();
        List<Contact> Cont = [Select id,Investor_s_PAN__c,Investor_Father_Husband_s_name__c,Postal_Address__c,Email,Phone,Name,Investor_Full_Name__c from Contact where id IN: InvestoridSet];
        System.debug('ContList>>' + Cont);
        For(Contact Ct : Cont)
            {
                InvestorInfoMap.Put(ct.id,ct);
            }     
        if(InvestorInfoMap != null && InvestorInfoMap.Size() > 0)
        {
          for(Investment__C Inv :InvList)
          {
            Inv.Investor_Name__c = InvestorInfoMap.get(Inv.Investor__c).Investor_Full_Name__c;
            Inv.Postal_Address__c = InvestorInfoMap.get(Inv.Investor__c).Postal_Address__c;
            Inv.Email_ID__c = InvestorInfoMap.get(Inv.Investor__c).Email;
            Inv.Investor_s_PAN__c = InvestorInfoMap.get(Inv.Investor__c).Investor_s_PAN__c;
            Inv.Investor_Father_Husband_s_name__c = InvestorInfoMap.get(Inv.Investor__c).Investor_Father_Husband_s_name__c;
            Inv.Phone_No__c = InvestorInfoMap.get(Inv.Investor__c).Phone;
          } 
        }
        
        Map<Id , Investment__c> parentInvestmentMap = new Map<Id , Investment__c>([SELECT Id  , Number_Of_Shares__c FROM Investment__c WHERE Id IN :parentInvestmentId.values()]);

        for(Investment__c inv : invList)
        {

            //  Added By Sahilparvat To Calculate The "Exit Type" On 21.06.2024
            if(inv.Parent_Investment__c != null)
            {
                Investment__c parentInvestment = parentInvestmentMap.get(parentInvestmentId.get(inv.Id));
                if((parentInvestment.Number_Of_Shares__c != null && inv.Number_Of_Shares__c != null) && parentInvestment.Number_Of_Shares__c == inv.Number_Of_Shares__c && inv.Type__c == 'Exit')
                {
                    inv.Exit_Type__c = 'Full Exit';
                }
                
                if((parentInvestment.Number_Of_Shares__c != null && inv.Number_Of_Shares__c != null) && parentInvestment.Number_Of_Shares__c != inv.Number_Of_Shares__c && inv.IRR_Value__c > 0 && inv.Type__c == 'Exit')
                {
                    inv.Exit_Type__c = 'Partial Exit';
                }
            }
            
            if(inv.IRR_Value__c == 0)
            {
                inv.Exit_Type__c = 'Capital Return';
            }
               
            if(inv.IRR_Value__c < 0)
            {
                inv.Exit_Type__c = 'Liquidation';
            }

            if(inv.Funds_Cheque__c && inv.Funds_Cheque__c != oldInvMap.get(inv.Id).Funds_Cheque__c && inv.Type__c == 'Committed')
            {
                inv.Type__c = 'Invested';
            }
            
            if(inv.Type__c =='Exit' && inv.Parent_Investment__c!=null && (inv.Exit_Date__c != oldInvMap.get(inv.Id).Exit_Date__c || inv.Exit_amount_to_be_transferred__c != oldInvMap.get(inv.Id).Exit_amount_to_be_transferred__c)) 
            {
                exitInvList.add(inv);
            }  
            if(inv.Account__c!=null)
                accountMap.put(inv.Account__c,null);
        }
        
        if(accountMap!=null && accountMap.size()>0)
        {            
            for(Account acc : [select id,name,Get_Physical_Certificate__c,Primary_Contact__c,Official_Email__c,Personal_Email__c from Account where id in : accountMap.keyset()])
            {
                accountMap.put(acc.id,acc);
            }
        }
        
        for(Investment__c inv : invList)
        {
            if(accountMap!=null && accountMap.size()>0 && accountMap.get(inv.account__c)!=null)
            {
                inv.Phone_No__c = accountMap.get(inv.account__c).Primary_Contact__c;
                inv.Get_Physical_Certificate__c = accountMap.get(inv.account__c).Get_Physical_Certificate__c;   
                inv.Official_Email__c = accountMap.get(inv.account__c).Official_Email__c;                  
                inv.Personal_Email__c = accountMap.get(inv.account__c).Personal_Email__c;                  
            }
        }    
        
        if(exitInvList!=null && exitInvList.size()>0)
        {
            calculateIRR(exitInvList,null);
        }
   
    }
    
    public List<Investment__c> calculateIRR(List<Investment__c> invList,Map<Id,Decimal> roundIssuePriceMap)
    {
        Map<Id,Investment__c> invParentMap = new Map<Id,Investment__c>();
        //Map<Id,Id> invStartupMap = new Map<Id,Id>();
        //Map<Id,Id> startupRoundMap = new Map<Id,Id>();
        //Map<String,List<Investment__c>> invStartupExitMap = new Map<String,List<Investment__c>>();
        List<Investment__c> invExitingList = new List<Investment__c>();
        List<Investment__c> invToUpdateList = new List<Investment__c>();
        Map<Id,Startup_Round__c> invRoundMap = new Map<Id,Startup_Round__c>();
        
        system.debug('roundIssuePriceMap>>>>>>'+roundIssuePriceMap);
        
        for(Investment__c inv : invList)
        {            
            if(inv.Type__c =='Exit' && inv.Parent_Investment__c!=null)// || inv.Type__c =='Partial Exit')
            {
                system.debug('before insert INV>>>>>>'+inv);
                invParentMap.put(inv.Parent_Investment__c,new Investment__c());
                invRoundMap.put(inv.Startup_Round__c,new Startup_Round__c());
            }    
        }
        
        system.debug('invParentMap>>>>'+invParentMap);
        if(invParentMap.size()>0)
        {            
            invExitingList = [Select Id,Investor__c,Startup_Round__c,Startup_Round__r.Issue_Price__c,Startup_Round__r.Startup__c,Investment_Date__c,Investment_Amount__c,Type__c,Membership_Validity__c from Investment__c where id in : invParentMap.keyset() order by Investment_Date__c];
            invRoundMap = new Map<Id,Startup_Round__c>([select id,name,Investment_Date_AIF_Platform_Mismatch__c,Exit_Price__c,Date_Of_Exit__c from Startup_Round__c where id in : invRoundMap.keyset()]);
            
            
            if(invExitingList.size()>0 && invRoundMap.size()>0)
            {
                for(Investment__c inv1 : invExitingList)
                {
                    invParentMap.put(inv1.Id,inv1);
                }
                
                System.debug('invParentMap>>>>>'+invParentMap);
                Decimal IRRCal = 0.0;
                
                for(Investment__c inv : invList)
                {   
                    //System.debug('invStartupExitMap11111>>>>>'+invStartupExitMap.containsKey(inv.Investor__c+'a0C0l00000E5esjEAB'));
                    //String keyVal = inv.Investor__c +''+ startupRoundMap.get(inv.Startup_Round__c);
                    
                    if(inv.Type__c =='Exit' && inv.Parent_Investment__c!=null && invParentMap.containsKey(inv.Parent_Investment__c))
                    {
                        //Decimal sharePrice1 = invParentMap.get(inv.Parent_Investment__c).Investment_Amount__c;
                        //Decimal sharePrice1 = inv.Number_Of_Shares__c * invParentMap.get(inv.Parent_Investment__c).Startup_Round__r.Issue_Price__c;
                        //Decimal sharePriceN = inv.Exit_amount_to_be_transferred__c;
                        
                        System.debug('inv 222>>>>>'+inv);
                        
                        Decimal sharePrice1;
                        Decimal sharePriceN = inv.Exit_Price__c;
                        
                        if(roundIssuePriceMap!=null && roundIssuePriceMap.containsKey(inv.Startup_Round__c))
                            sharePrice1 = roundIssuePriceMap.get(inv.Startup_Round__c);
                        else
                            sharePrice1 = invParentMap.get(inv.Parent_Investment__c).Startup_Round__r.Issue_Price__c;
                        
                        system.debug('Issue_Price__c>>>>>>'+sharePrice1);
                        system.debug('Exit_Price__c>>>>>>'+sharePriceN);
                        
                        Double sharCal = 0.0;
                        Double periodCal = 0.0;
                        System.debug('invParentMap Parent>>>'+invParentMap.get(inv.Parent_Investment__c));
                        System.debug('invParentMap Parent>>>'+invParentMap.get(inv.Parent_Investment__c).Investment_Date__c);
                        Date date1 = invParentMap.get(inv.Parent_Investment__c).Investment_Date__c;
                        Date dateN = inv.Exit_Date__c;
                        
                      
                        
                        System.debug('date1 >>>>>'+date1 );
                        /* Commented by Hemant as suggested by Shubham,Yashika,Jay to add 10 days instead of 7 | Date: 02-03-2023
                        date1 = date1.addDays(7);
                        */
                        date1 = date1.addDays(10);
                        
                        System.debug('date1 after 10 days >>>>>'+date1 );
                        System.debug('dateN >>>>>'+dateN );
                        Decimal numberDaysDue=0;
                        
                        if(date1!=null && dateN!=null)
                        {
                            numberDaysDue = date1.daysBetween(dateN);
                            
                            System.debug('numberDaysDue >>>>>'+numberDaysDue );
                            
                            //As per new IRR calculation given Chaitanya
                            //numberDaysDue = numberDaysDue + 7;
                            
                            System.debug('(date1.monthsBetween(dateN)) / 365 * 12>>>>>'+(date1.monthsBetween(dateN)) / 365 * 12);
                            System.debug('(date1.monthsBetween(dateN))>>>>>'+(date1.monthsBetween(dateN)));
                            System.debug('(date1.monthsBetween(dateN)) / 365>>>>>'+(date1.monthsBetween(dateN)) / 365);
                            
                            
                            Decimal numberMonthDiff = date1.monthsBetween(dateN);
                            
                            If(numberMonthDiff < 1){
                                numberMonthDiff = 1;
                            }  
                            
                            System.debug('numberMonthDiff >>>>>'+numberMonthDiff );
                            
                            /* Commented by Hemant as suggested by Shubham,Jay,Yashika| Date: 02-03-2023
                            if(invRoundMap.containsKey(inv.Startup_Round__c) && invRoundMap.get(inv.Startup_Round__c).Investment_Date_AIF_Platform_Mismatch__c)
                            {
                            System.debug('invRoundMap.get(inv.Startup_Round__c).Investment_Date_AIF_Platform_Mismatch__c>>>>>'+invRoundMap.get(inv.Startup_Round__c).Investment_Date_AIF_Platform_Mismatch__c);
                            numberMonthDiff = 1;
                            } 
                            */
                            
                            Decimal period = numberDaysDue/365;
                            
                            if(numberMonthDiff < 6)
                                period = 365/numberDaysDue;
                            
                            if(period <0)
                                period = period * -1;
                            
                            System.debug('period >>>>>'+period );
                            System.debug('sharePrice1 >>>>>'+sharePrice1 );
                            System.debug('sharePriceN >>>>>'+sharePriceN );
                            
                            if(sharePrice1>0 && sharePriceN!=null && sharePriceN>0)
                                sharCal = sharePriceN/sharePrice1;
                            if(period>0)
                            {
                                if(numberMonthDiff >=6)
                                    periodCal = 1/period;
                                else
                                    periodCal = period;
                            }
                            System.debug('period >>>>>'+period );
                            System.debug('sharCal >>>>>'+sharCal );
                            System.debug('periodCal >>>>>'+periodCal );
                            System.debug('numberDaysDue>>>>>'+numberDaysDue);
                            System.debug('numberMonthDiff >>>>>'+numberMonthDiff );
                            
                            if(numberMonthDiff >=6)
                            {
                                IRRCal = Math.pow(sharCal ,periodCal);
                                IRRCal = IRRCal -1;
                            }
                            else
                            {
                                IRRCal = (sharCal - 1) * periodCal;
                            }
                            
                            IRRCal = IRRCal * 100;
                            inv.IRR_Value__c = IRRCal;
                            //invToUpdateList.add(inv);
                        } 
                            
                        
                        //Code is writtern temporary to calculate the IRR with round's exit date1
                        //Date: 03-05-2023    
                        dateN = invRoundMap.get(inv.Startup_Round__c).Date_Of_Exit__c;
                        
                        System.debug('date1 >>>>>'+date1 );
                        
                        System.debug('date1 after 10 days >>>>>'+date1 );
                        System.debug('dateN startup Exit Date >>>>>'+dateN );
                        numberDaysDue=0;
                        
                        if(date1!=null && dateN!=null)
                        {
                            numberDaysDue = date1.daysBetween(dateN);
                            
                            System.debug('numberDaysDue >>>>>'+numberDaysDue );
                            
                            //As per new IRR calculation given Chaitanya
                            //numberDaysDue = numberDaysDue + 7;
                            
                            System.debug('(date1.monthsBetween(dateN)) / 365 * 12>>>>>'+(date1.monthsBetween(dateN)) / 365 * 12);
                            System.debug('(date1.monthsBetween(dateN))>>>>>'+(date1.monthsBetween(dateN)));
                            System.debug('(date1.monthsBetween(dateN)) / 365>>>>>'+(date1.monthsBetween(dateN)) / 365);
                            
                            
                            Decimal numberMonthDiff = date1.monthsBetween(dateN);
                            
                            If(numberMonthDiff < 1){
                                numberMonthDiff = 1;
                            }  
                            
                            System.debug('numberMonthDiff >>>>>'+numberMonthDiff );
                                                        
                            Decimal period = numberDaysDue/365;
                            periodCal = 0.0;
                            if(numberMonthDiff < 6)
                                period = 365/numberDaysDue;
                            
                            if(period <0)
                                period = period * -1;
                            
                            System.debug('period >>>>>'+period );
                            System.debug('sharePrice1 >>>>>'+sharePrice1 );
                            System.debug('sharePriceN >>>>>'+sharePriceN );
                            
                            if(sharePrice1>0 && sharePriceN!=null && sharePriceN>0)
                                sharCal = sharePriceN/sharePrice1;
                            if(period>0)
                            {
                                if(numberMonthDiff >=6)
                                    periodCal = 1/period;
                                else
                                    periodCal = period;
                            }
                            System.debug('period >>>>>'+period );
                            System.debug('sharCal >>>>>'+sharCal );
                            System.debug('periodCal >>>>>'+periodCal );
                            System.debug('numberDaysDue>>>>>'+numberDaysDue);
                            System.debug('numberMonthDiff >>>>>'+numberMonthDiff );
                            
                            IRRCal = 0;
                            if(numberMonthDiff >=6)
                            {
                                IRRCal = Math.pow(sharCal ,periodCal);
                                IRRCal = IRRCal -1;
                            }
                            else
                            {
                                IRRCal = (sharCal - 1) * periodCal;
                            }
                            
                            IRRCal = IRRCal * 100;
                            inv.IRR_Realised_New__c = IRRCal;
                        }
                        
                        invToUpdateList.add(inv);
                         
                    }     
                }
                
                //update invList;
                System.debug('IRRCal>>>>>'+IRRCal);
                System.debug('IRRCal>>>>>'+IRRCal);
            }  
        }  
        System.debug('invToUpdateList>>>>>'+invToUpdateList);
        return invToUpdateList;
    }
    
    
    
    public void afterInsert(List<Investment__c> invList)
    {
        accInvMap = new Map<Id,List<Investment__c>>();
        contInvSet = new Set<Id>();
        Set<Id> invForAPI1 = new Set<Id>();
        Set<Id> invForAPI2 = new Set<Id>();
        Set<Id> setPointTransId = new Set<Id>();
        Integer count = 0;
        Boolean callInvAPI = false;
        Map<Id,Decimal> invertorAmountMap = new Map<Id,Decimal>();
                List<Investment__c> pointTransactionCreation = new List<Investment__c>();
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('InvestmentTriggerHandler API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Investment_API_Call__c)
            callInvAPI = true;
        
        //Added by Karan 19/09/2022:
        Set<id> StrtRundIds = new set<id>();
        map<Id,decimal> mapCATotal = new map<Id,decimal>();
        for(Investment__c inv : invList)
        {
            StrtRundIds.add(inv.Startup_Round__c);
            //Commented by ankush as disscussed with Hemant
            /* if(!RecursiveHandler.invIdsTransactionSet.contains(inv.Id) && inv.Type__c=='Invested')
            {
            invertorAmountMap.put(inv.Investor__c,inv.Investment_Amount__c);
            RecursiveHandler.invIdsTransactionSet.add(inv.Id);
            }*/
            ///*************************//
            
            decimal total=0;
            if(inv.Contribution_Agreement__c != null && inv.Type__c == 'Committed'){
                total += inv.Committed_Amount__c;
                mapCATotal.put(inv.Contribution_Agreement__c,total);
            }
            if((inv.Type__c == 'Invested' && inv.Investment_Amount__c >= 490000) || (inv.Type__c == 'Back out unapproved'))
            {
                pointTransactionCreation.add(inv);
            }
        }
        
        
        List<Contribution_Agreement__c> lstCA = [SELECT Id,Total_Committed_Amount__c FROM Contribution_Agreement__c WHERE Id IN :mapCATotal.keySet()];
        for(Contribution_Agreement__c ca : lstCA){
            ca.Total_Committed_Amount__c = mapCATotal.get(ca.id);
        }
        update lstCA; 
        //**********************************//
        system.debug('After Insert StrtRundIds Ids>>>>>>>>'+StrtRundIds.size());
        List<Startup_Round__c> strtupListTobalanceOfShare = [Select Id, Balance_Of_Share__c,No_of_Investment__c,(Select Id,Startup_Round__c From Investments__r) From Startup_Round__c Where Id IN: StrtRundIds];
        for(Startup_Round__c strtupR: strtupListTobalanceOfShare){
            strtupR.No_of_Investment__c =  strtupR.Investments__r.size();
        }
        update strtupListTobalanceOfShare;
        
        //Added by karan for Balance of sale 30/06/2022.
        for(Investment__c inv : invList)
        {
            if(inv.Startup_Round__c!=null)
            {
                strtupInvIDs.add(inv.Startup_Round__c);
            }
        }
        system.debug('Investment Ids>>>>>>>>'+strtupInvIDs.size());
        aggregateResults();
        
        //Added by Ankush for deal tracker 25/8/2022.
        For(Investment__c Inv: invList)
        {   
            StrtupRoundIds.add(Inv.Startup_round__c);
            
        }
        StrtupRoundIds.remove(null);
        System.debug('StartupIds'+StrtupRoundIds);
        startupRoundRollup();
        
        
        for(Investment__c inv : invList)
        {            
            Boolean jointFlag = (inv.Investment_in_Own_Name_Family_Member__c==JOINTSSTR && !inv.Is_Primary__c) ? false : true;
            
            if(callInvAPI && inv.Type__c !='Round Closed - Commitment Released' && jointFlag )
            {
                if(count < 100)
                    invForAPI1.add(inv.Id);
                else
                    invForAPI2.add(inv.Id);
                
                count ++;
            }
            
            if(inv.Type__c =='Back out unapproved' || inv.Type__c =='Waitlist' || inv.Type__c=='Invested' 
               || !inv.SAF_soft_copy__c || !inv.Transfer_Deed_hard_copy__c || !inv.Deed_of_Adherence_hard_copy__c || !inv.DOA_stamp_paper_hard_copy__c
              )
            {
                accInvMap.put(inv.Account__c,new List<Investment__c>());
            }
            
            if(inv.Investor__c!=null && inv.Investor_Type__c == 'Via AIF' && (inv.Committed_Amount__c!=null || inv.Investment_Amount__c!=null))
            {
                contInvSet.add(inv.Investor__c);
            }
        }
        system.debug('AFter Insert RecursiveHandler.isInvAPICall>>>>>>'+RecursiveHandler.isInvAPICall);
        system.debug('AFter Insert invForAPI1>>>>>>'+invForAPI1);
        system.debug('AFter Insert invForAPI12>>>>>>'+invForAPI2);
        if(callInvAPI && RecursiveHandler.isInvAPICall)
        {
            RecursiveHandler.isInvAPICall = false;
            if(invForAPI1.size()>0){            
                InvestmentRestAPIController.sendInvestmentDetails(invForAPI1,true);
            }
            if(invForAPI2.size()>0){            
                InvestmentRestAPIController.sendInvestmentDetails(invForAPI2,true);
            }
        }
        
        system.debug('accInvMap>>>>>'+accInvMap); 
        system.debug('contInvSet>>>>>'+contInvSet); 
        
        if(accInvMap!=null && accInvMap.size()>0)
        {
            //Temporary Commented by Hemant || Date: 11-05-2024 || Getting "You can not edit this field because these fields are locked right now" Error for bulk update and exit module and getting APEX CPU time limits and platform limitations
            //updateStartupInvRollup(accInvMap);
        }
        
        if(contInvSet!=null && contInvSet.size()>0)
        {
            system.debug('contInvSet111>>>>>'+contInvSet.size()); 
            updateContactInvRollup(contInvSet);
        }
        
        if(invertorAmountMap.size()>0){
            // createTransactionObj(invertorAmountMap);
        }
        handleInvestmentActivity(invList , new Map<Id,Investment__c>());
        
        if(pointTransactionCreation.size() > 0)
        {
            createPointTransaction(pointTransactionCreation);
        }
        ReferralTaskCreation.taskCreationOnInvestmentInsert(invList);
        System.debug('Referall Class calledd >>>'+invList);
         
        set<Id> Invids = new set<Id>();
         for (Investment__c inv : invList) {
         Invids.add(inv.Id);
               }
           ReferralTaskCreation.checkMilestoneCrossing(Invids);
        system.debug('investment more than 10 lakhs>>>>>>');
    }
    
    public void afterUpdate(List<Investment__c> invList,Map<id,Investment__c> oldInvMap)
    {
        system.debug('RecursiveHandler.accountIdForRollUp>>>>>>'+RecursiveHandler.accountIdForRollUp);
        accInvMap = new Map<Id,List<Investment__c>>();
        contInvSet = new Set<Id>();
        // Map<Id,String> invWhatsAppAPITypeMap = new Map<Id,String>(); Old Approach To Send Only 1 WhatsApp BOT MSG 
        Map<Id, List<String>> invWhatsAppAPITypeMap = new Map<Id, List<String>>(); //New Approach To Send 2 WhatsApp BOT MSG At Once.
        // Map<Id,String> invWhatsAppAPIFeeChequeMap = new Map<Id,String>(); Old Approach To Send Only 1 WhatsApp BOT MSG 
        Map<Id, List<String>> invWhatsAppAPIFeeChequeMap = new Map<Id, List<String>>(); //New Approach To Send 2 WhatsApp BOT MSG At Once.
        Set<Id> invForAPI1 = new Set<Id>();
        Set<Id> invIdForEmailNotif = new Set<Id>();
        Set<Id> invForAPI2 = new Set<Id>();
        Set<Id> invDeleteIdsAPI1 = new Set<Id>();
        Set<Id> invDeleteIdsAPI2 = new Set<Id>();
        List<Investment__c> invForUserActivity = new List<Investment__c>();
        List<Investment__c> invForPointTransaction = New List<Investment__c>();
        Integer countUPD = 0;
        Integer countDLT = 0;
        Boolean callInvAPI = false;
        Boolean callWhatsappAPI = false;
        Map<Id,Decimal> invertorAmountMap = new Map<Id,Decimal>();
        // Added by Karan Code for Auto Com Switch
        Map<id,investment__c> InvSwitchMap = new Map<id,Investment__C>();
        Map<id,investment__c> RoundSwitchMap = new Map<id,investment__c>();
        Set<Id> investmentIds = new Set<Id>();
        
        
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('InvestmentTriggerHandler API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Investment_API_Call__c)
            callInvAPI = true;
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Whatsapp_API_Call__c)
            callWhatsappAPI = true;
        
        //Added by Karan 19/09/2022:
        Set<id> StrtRundIds = new set<id>();
        Set<id> CAIds = new set<id>();
        decimal total = 0;
        map<Id,decimal> mapCATotal = new map<Id,decimal>();
        for(Investment__c inv : invList)
        {
            System.debug('current inv record in loop >>>>>>> ' + inv);
            StrtRundIds.add(inv.Startup_Round__c);// investment Rounds changed
            Investment__c oldValuesOnThatInvestment = oldInvMap.get(inv.id);
            StrtRundIds.add(oldValuesOnThatInvestment.Startup_Round__c);
            system.debug('inv123098>>>>>>>>'+inv.Id);
            system.debug('invType__c>>>>>>>>'+inv.Type__c );
            system.debug('oldInvMap.get(inv.Id)>>>>>>>>'+oldInvMap.get(inv.Id).Type__c);

            if (oldInvMap.get(inv.Id).Type__c != inv.Type__c && 
               ((oldInvMap.get(inv.Id).Type__c != 'Back out unapproved' && inv.Type__c == 'Back out unapproved') || 
               (oldInvMap.get(inv.Id).Type__c != 'Invested' && inv.Type__c == 'Invested'))) {
                    System.debug('OLD MAP VALUE >>>>>> ' + oldInvMap.get(inv.Id).Type__c);
                    System.debug('NEW MAP VALUE >>>>>> ' + inv.Type__c);
                    invForPointTransaction.add(inv);
            }

            if (oldInvMap.get(inv.Id).Type__c != inv.Type__c && 
               ((oldInvMap.get(inv.Id).Type__c != 'Committed' && inv.Type__c == 'Committed') || 
               (oldInvMap.get(inv.Id).Type__c != 'Invested' && inv.Type__c == 'Invested'))) {
                    System.debug('OLD MAP VALUE User Activity >>>>>> ' + oldInvMap.get(inv.Id).Type__c);
                    System.debug('NEW MAP VALUE User Activity >>>>>> ' + inv.Type__c);
                    invForUserActivity.add(inv);
            }

            //Added by Hemant for transaction records 
            /*if(!RecursiveHandler.invIdsTransactionSet.contains(inv.Id) && oldInvMap.get(inv.Id).Type__c!=inv.Type__c && inv.Type__c=='Invested' && oldInvMap.get(inv.Id).Type__c=='Committed')
            {
            invertorAmountMap.put(inv.Investor__c,inv.Investment_Amount__c);
            RecursiveHandler.invIdsTransactionSet.add(inv.Id);
            } */
            /**************************/
            
            if(inv.Contribution_Agreement__c != null){
               CAIds.add(inv.Contribution_Agreement__c);
               if(oldInvMap != null && oldInvMap.size() > 0){
                    if(oldInvMap.get(inv.Id).Contribution_Agreement__c != inv.Contribution_Agreement__c){
                        CAIds.add(oldInvMap.get(inv.Id).Contribution_Agreement__c);
                    }
                }
            }
            
        }
        if(!System.isFuture() && !System.isBatch()){
            ContributionTriggerHandler.updateCACommittTotal(CAIds);
        }
        
        /****************************/
        system.debug('invertorAmountMap1234>>>>>>>>'+invertorAmountMap);
        system.debug('After Update StrtRundIds Ids>>>>>>>>'+StrtRundIds.size());
        List<Startup_Round__c> strtupListTobalanceOfShare = [Select Id, Balance_Of_Share__c,No_of_Investment__c,(Select Id,Startup_Round__c From Investments__r) From Startup_Round__c Where Id IN: StrtRundIds];
        for(Startup_Round__c strtupR: strtupListTobalanceOfShare){
            strtupR.No_of_Investment__c =  strtupR.Investments__r.size();
        }
        update strtupListTobalanceOfShare;
        
        //Added by karan for balance of sale 30/06/2022.
        for(Investment__c inv : invList)
        {
            if(inv.Startup_Round__c!=null && (inv.Intransfer_balance_share__c != oldInvMap.get(inv.Id).Intransfer_balance_share__c || inv.Invested_number_of_share__c != oldInvMap.get(inv.Id).Invested_number_of_share__c || inv.IPVHQ_Shadow_Balance_Share__c != oldInvMap.get(inv.Id).IPVHQ_Shadow_Balance_Share__c|| inv.IPVFnF_Shadow_Balance_share__c != oldInvMap.get(inv.Id).IPVFnF_Shadow_Balance_share__c|| inv.Family_Balance_Share__c != oldInvMap.get(inv.Id).Family_Balance_Share__c))
            {
                strtupInvIDs.add(inv.Startup_Round__c);
                if(oldInvMap.get(inv.id).Startup_Round__c!=inv.Startup_Round__c)
                {
                    strtupInvIDs.add(oldInvMap.get(inv.id).Startup_Round__c);
                }
            }
            
        }
        if(strtupInvIDs.size() > 0){
            aggregateResults();
        }
        
        //Added by ankush for deal tracker 24/8/22
        for(Investment__c inv: invlist)
        {
            
            if(inv.Startup_Round__c!= null && (Inv.Number_Of_Shares__c != oldInvMap.get(Inv.id).Number_Of_Shares__C ||inv.Investor_Type__c != oldInvMap.get(inv.id).Investor_type__c || inv.call_for_money_sent__c != oldInvMap.get(inv.id).call_for_money_sent__c || inv.Date_of_FnF_CFM__c != oldInvMap.get(inv.Id).Date_of_FnF_CFM__c || inv.Investment_amount__c!= oldInvMap.get(inv.Id).Investment_amount__c ||inv.type__c != oldInvMap.get(inv.Id).type__c || inv.Issue_Type__c != oldInvMap.get(inv.Id).issue_type__c ||inv.Investment_Amount_Due__c != oldInvMap.get(inv.Id).Investment_Amount_Due__c))
            {
                StrtupRoundIds.add(inv.Startup_Round__c);
                if(OldInvMap.get(inv.id).Startup_Round__c != inv.Startup_round__c)
                {
                    StrtupRoundIds.add(oldInvMap.get(inv.id).Startup_round__c);
                }
            }
        }
        
        if(StrtupRoundIds.size() > 0){
            startupRoundRollup();
        }
        // Added by Karan Code for Auto Com Switch-- modified to bulkify 26/05/2023.
        
         for (Investment__c inv : invList) {
            investmentIds.add(inv.Id);
        }
        Map<Id, Investment__c> autoComSwitch = new Map<Id, Investment__c>([SELECT Id, Investor__r.Send_Auto_Com_Email__c, Startup_Round__r.Send_Auto_Comms_Email__c FROM Investment__c WHERE Id IN :investmentIds]);
        for (Investment__c inv : invList) {
            if (autoComSwitch.containsKey(inv.Id)) {
                Investment__c invt = autoComSwitch.get(inv.Id);
                if (invt.Investor__r.Send_Auto_Com_Email__c != null) {
                    invSwitchMap.put(invt.Id, invt);
                    // InvestorSwitch = invt.Investor__r.Send_Auto_Com_Email__c;
                }
                if (invt.Startup_Round__r.Send_Auto_Comms_Email__c != null) {
                    roundSwitchMap.put(invt.Id, invt);
                    // RoundSwitch = invt.Startup_Round__r.Send_Auto_Comms_Email__c;
                }
            }
        } 
        
        
        for(Investment__c inv : invList)
        {
            system.debug('inv123>>>>>>'+inv.Id);
            if(callInvAPI)
            {
                if(inv.Type__c =='Round Closed - Commitment Released' || (inv.Investment_in_Own_Name_Family_Member__c==JOINTSSTR && !inv.Is_Primary__c))
                {
                    if(countDLT < 100)
                        invDeleteIdsAPI1.add(inv.Id);
                    else
                        invDeleteIdsAPI2.add(inv.Id);
                    
                    countDLT ++;                                  
                }
                else
                {
                    if(countUPD < 100)
                        invForAPI1.add(inv.Id);
                    else
                        invForAPI2.add(inv.Id);
                    
                    countUPD ++;                
                }
            }
            
            //Modified by karan for autocommunication 10/04/23.
            /*  if(inv.IPV_Fees_Cheque__c!= oldInvMap.get(inv.ID).IPV_Fees_Cheque__c && inv.IPV_Fees_Cheque__c=='Yes' ) 
            {
                invWhatsAppAPIFeeChequeMap.put(inv.Id,'ipv_investment_fee_received_confirmation');
                if(!RecursiveHandler.invIdsEmailNotification.contains(inv.Id)) 
                    invIdForEmailNotif.add(inv.Id);
            }*/
            
            // Auto Com bot Messages Code.
            if(inv.IPV_Fees_Cheque__c!= oldInvMap.get(inv.ID).IPV_Fees_Cheque__c && inv.IPV_Fees_Cheque__c=='Yes' &&  InvSwitchMap.get(inv.id).Investor__r.Send_Auto_Com_Email__c==true && RoundSwitchMap.get(inv.id).Startup_Round__r.Send_Auto_Comms_Email__c == true && Inv.Issue_Type__c !='Friends & Family T2'
               && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='IPV HQ - Shadow' || inv.Issue_Type__c=='IPV FnF - Shadow' || inv.Issue_Type__c==' IPV Employee')) 
            {
                if (!invWhatsAppAPIFeeChequeMap.containsKey(inv.Id)) {
                    invWhatsAppAPIFeeChequeMap.put(inv.Id, new List<String>());   
                }
                // invWhatsAppAPIFeeChequeMap.get(inv.Id).add('ipv_investment_feereceived_confirmation');
                 if(!RecursiveHandler.invIdsEmailNotification.contains(inv.Id))
                 {
                  //     invIdForEmailNotif.add(inv.Id);
                 }
            }
            // Auto Com bot Messages Code.
            System.debug('callWhatsappAPI>>>>>'+callWhatsappAPI );
            if(callWhatsappAPI && ((inv.Type__c != oldInvMap.get(inv.Id).Type__c) || (oldInvMap.get(inv.ID).IPV_Fees_Cheque__c != 'Yes' && inv.IPV_Fees_Cheque__c!= oldInvMap.get(inv.ID).IPV_Fees_Cheque__c && inv.IPV_Fees_Cheque__c=='Yes')) &&  InvSwitchMap.get(inv.id).Investor__r.Send_Auto_Com_Email__c==true && RoundSwitchMap.get(inv.id).Startup_Round__r.Send_Auto_Comms_Email__c == true)
            {

                System.debug('Issue Type >>>> ' + inv.Issue_Type__c);
                System.debug('Type >>>> ' + inv.Type__c);
                System.debug('Membership Status >>>> ' + inv.Membership_status__c);

                if(inv.Type__c == 'Invested' && inv.Issue_Type__c != 'Friends & Family T1'&& inv.Issue_Type__c != 'Friends & Family T2'){

                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    //  Commented By Sahilparvat As Discussed With Mauli Ma'am on 21.02.2025 Because of Ishita Said It's Not Required Anymore
                    //invWhatsAppAPITypeMap.get(inv.Id).add('fund_transfered_confirmation');
                    if(!RecursiveHandler.invIdsEmailNotification.contains(inv.Id)) 
                        invIdForEmailNotif.add(inv.Id);
                }

                if(oldInvMap.get(inv.ID).IPV_Fees_Cheque__c != 'Yes' && inv.IPV_Fees_Cheque__c!= oldInvMap.get(inv.ID).IPV_Fees_Cheque__c && inv.IPV_Fees_Cheque__c=='Yes' && Inv.Issue_Type__c !='Friends & Family T2'
                && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='IPV HQ - Shadow' || inv.Issue_Type__c=='IPV FnF - Shadow' || inv.Issue_Type__c=='IPV Employee'))
                {
                    System.debug('Inside Scenario 1 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('ipv_investment_feereceived_confirmation');
                }
                
                if(inv.Type__c == 'Back out approved')
                {
                    System.debug('Inside Scenario 2 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('back_out_request');
                }
                
                if(inv.Type__c == 'Back out unapproved')
                {
                    System.debug('Inside Scenario 3 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('unapproved_back_out_pdf');
                    //invWhatsAppAPITypeMap1.put(inv.Id,'unapproved_back_out_pdf');
                }
                
                //Confirmation for waitlist members
                if(inv.Type__c=='Committed' && (inv.Membership_status__c=='Paid IPV Fee' || inv.Membership_status__c=='Paid Community' || inv.Membership_status__c=='Complimentary' || inv.Membership_status__c=='Paid by IPV Points' || inv.Membership_status__c=='Paid by CXO Points' || inv.Membership_status__c=='Add On' || inv.Membership_status__c=='IPV Team')
                && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Primary - AIF' || inv.Issue_Type__c=='Friends & Family T1 - AIF' || inv.Issue_Type__c=='Friends & Family T2 - AIF' || inv.Issue_Type__c==' IPV Employee'))
                {
                    System.debug('Inside Scenario 4 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('confirmation_for_waitlist_members_confirmed_by_the_lead_member');
                }
                
                if(inv.Type__c == 'Round Closed - Commitment Released')
                {
                    System.debug('Inside Scenario 5 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('commitment_released_confirmation_round_closed');    
                }
                
                if(inv.Type__c == 'Round closed - deal dropped')
                {
                    System.debug('Inside Scenario 6 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('commitment_released_confirmation_due_to_deal_dropped');   
                }
                
                if(inv.Type__c=='Invested' && (inv.Issue_Type__c =='Primary'))
                {
                    System.debug('Inside Scenario 7 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('platform_investment_amount_received_confirmation');
                }
                if(inv.Type__c=='Invested' && (inv.Issue_Type__c =='Primary - AIF' || inv.Issue_Type__c =='Friends & Family T1 - AIF' || inv.Issue_Type__c =='Friends & Family T2 - AIF'))
                {
                    System.debug('Inside Scenario 8 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('aif_investment_amount_received_confirmation');
                }
                
                if(inv.Type__c == 'Invested' && inv.Issue_Type__c == 'Friends & Family T2' && inv.IPV_Fees_Cheque__c=='Yes')
                {
                    System.debug('Inside Scenario 9 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('fnf_t2_investment_amount_received_confirmation');   
                }
                
                if(inv.Type__c == 'Invested' && inv.Issue_Type__c == 'Friends & Family T1')
                {
                    System.debug('Inside Scenario 10 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('fnf_t1_investment_amount_received_confirmation');
                }
                  
                if(inv.Type__c == 'Committed' && (inv.Residential_Status__c=='NRI' || inv.Residential_Status__c=='OCI Holder') && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Friends & Family T1' || inv.Issue_Type__c=='Friends & Family T2'))
                {
                    System.debug('Inside Scenario 11 >>>> ');
                    if (!invWhatsAppAPITypeMap.containsKey(inv.Id)) {
                        invWhatsAppAPITypeMap.put(inv.Id, new List<String>());   
                    }
                    invWhatsAppAPITypeMap.get(inv.Id).add('nri_communication');  
                    System.debug('The WhatsApp Map Is >>>>> ' + invWhatsAppAPITypeMap);
                }
                
            }
            if(!RecursiveHandler.accountIdForRollUp.contains(inv.Account__c) && (inv.SAF_soft_copy__c!=oldInvMap.get(inv.Id).SAF_soft_copy__c
                                                                                 || inv.Transfer_Deed_hard_copy__c !=oldInvMap.get(inv.Id).Transfer_Deed_hard_copy__c
                                                                                 || inv.Deed_of_Adherence_hard_copy__c !=oldInvMap.get(inv.Id).Deed_of_Adherence_hard_copy__c
                                                                                 || inv.DOA_stamp_paper_hard_copy__c !=oldInvMap.get(inv.Id).DOA_stamp_paper_hard_copy__c
                                                                                 ||(inv.Type__c!=oldInvMap.get(inv.ID).Type__c && 
                                                                                    (inv.Type__c =='Back out unapproved' || inv.Type__c =='Waitlist' || inv.Type__c=='Invested'
                                                                                     || oldInvMap.get(inv.ID).Type__c =='Back out unapproved' || oldInvMap.get(inv.ID).Type__c =='Waitlist' || oldInvMap.get(inv.ID).Type__c=='Invested'
                                                                                    )
                                                                                   )
                                                                                ))
            {
                
                accInvMap.put(inv.Account__c,new List<Investment__c>());
            }
            
            if(inv.Investor__c!=null && (inv.Investor_Type__c == 'Via AIF' || oldInvMap.get(inv.Id).Investor_Type__c =='Via AIF') && 
               (inv.Investor_Type__c != oldInvMap.get(inv.Id).Investor_Type__c
                || inv.Final_Commitment_Amount__c!=oldInvMap.get(inv.Id).Final_Commitment_Amount__c || inv.Investment_Amount__c!=oldInvMap.get(inv.Id).Investment_Amount__c
                || (inv.Type__c!=oldInvMap.get(inv.Id).Type__c && (inv.Type__c=='Committed' || inv.Type__c=='Invested' || oldInvMap.get(inv.Id).Type__c =='Committed' || oldInvMap.get(inv.Id).Type__c =='Invested'))
               )
              )
            {
                contInvSet.add(inv.Investor__c);
            }
            
            if(!RecursiveHandler.invIdsEmailNotification.contains(inv.Id))
            {
                if(inv.IPV_Fees_Cheque__c!= oldInvMap.get(inv.ID).IPV_Fees_Cheque__c && inv.IPV_Fees_Cheque__c=='Yes' && InvSwitchMap.get(inv.id).Investor__r.Send_Auto_Com_Email__c==true && RoundSwitchMap.get(inv.id).Startup_Round__r.Send_Auto_Comms_Email__c == true 
                   && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='IPV HQ - Shadow' || inv.Issue_Type__c=='IPV FnF - Shadow' || inv.Issue_Type__c==' IPV Employee'))  
                {
                    invIdForEmailNotif.add(inv.Id);
                    RecursiveHandler.invIdsEmailNotification.add(inv.Id);
                }
                else if(inv.Type__c != oldInvMap.get(inv.Id).Type__c && InvSwitchMap.get(inv.id).Investor__r.Send_Auto_Com_Email__c==true && RoundSwitchMap.get(inv.id).Startup_Round__r.Send_Auto_Comms_Email__c == true && 
                        ( inv.Type__c=='Back out approved'
                         || inv.Type__c=='Back out unapproved'
                         || (inv.Type__c=='Committed' && (inv.Membership_status__c=='On Trial' || inv.Membership_status__c=='On Trial Community'))
                         || (inv.Type__c=='Committed' && ((inv.Residential_Status__c =='NRI' || inv.Residential_Status__c=='OCI Holder') && (inv.Issue_Type__c=='Primary' || inv.Issue_Type__c=='Friends & Family T1' || inv.Issue_Type__c=='Friends & Family T2')))
                         || (inv.Type__c=='Waitlist' && (inv.Reason_for_waitlist__c=='Over Subscription' || inv.Reason_for_waitlist__c=='Unapproved Back Outs' || inv.Reason_for_waitlist__c=='Pending Documents' || inv.Reason_for_waitlist__c=='IPV Fee Pending' || inv.Reason_for_waitlist__c=='MBA Oversubscription'))
                         || (inv.Type__c=='Invested' && (inv.Issue_Type__c =='Primary' || inv.Issue_Type__c =='Primary - AIF' || inv.Issue_Type__c =='Friends & Family T1 - AIF' || inv.Issue_Type__c =='Friends & Family T2 - AIF'))
                         || (inv.Type__c == 'Invested' && ((inv.Issue_Type__c == 'Friends & Family T2' && inv.IPV_Fees_Cheque__c=='Yes') || inv.Issue_Type__c == 'Friends & Family T1'))
                         || (inv.Type__c=='Committed' && (inv.Issue_Type__c=='Friends & Family T2' || inv.Issue_Type__c=='Friends & Family T1'))
                         || inv.Type__c=='Round Closed - commitment Released'
                         || inv.Type__c=='Round closed - deal dropped'
                         || inv.Type__c=='Committed'
                        ) 
                       )
                {
                    System.debug('AutoComSwitchInv Ask1'+ InvSwitchMap.get(inv.id).Investor__r.Send_Auto_Com_Email__c);
                    System.debug('AutoComSwitchStart Ask2'+RoundSwitchMap.get(inv.id).Startup_Round__r.Send_Auto_Comms_Email__c);
                    system.debug('oldInvMap.get(inv.Id).Type__c>>>>>>>>>>>'+oldInvMap.get(inv.Id).Type__c);
                    system.debug('inv.Type__c>>>>>>>>>>>'+inv.Type__c);
                    invIdForEmailNotif.add(inv.Id);
                    RecursiveHandler.invIdsEmailNotification.add(inv.Id);
                } 
            }
        } 
        
        system.debug('RecursiveHandler.invIdsEmailNotification>>>>>>'+RecursiveHandler.invIdsEmailNotification);
        system.debug('invIdForEmailNotif>>>>>>'+invIdForEmailNotif);
        if(invIdForEmailNotif!=null && invIdForEmailNotif.size()>0)
        {
            try{
                // Commented temperory As getting issue in prodution  18.7.23
                // sendEmailWithTemplate(invIdForEmailNotif,oldInvMap);
                EmailUtility cls = new EmailUtility();
                cls.sendEmailWithTemplate(invIdForEmailNotif,oldInvMap);
                System.debug('sendEmailWithTemplate called');
            }
            Catch(Exception e)
            {
                system.debug('Email Exception>>>>>>'+e.getMessage());
                Communication_History_Tracking__c historyObj = new Communication_History_Tracking__c();
                historyObj.Type__c = 'Email';
                historyObj.Communication_details__c = JSON.serialize('Error:'+e.getMessage()+' FOR '+invIdForEmailNotif);
                historyObj.Status__c = 'Failure';
                insert historyObj;
            }
        }
        
        system.debug('invWhatsAppAPITypeMap111>>>>>>'+invWhatsAppAPITypeMap);
        system.debug('RecursiveHandler.isInvAPICall>>>>>>'+RecursiveHandler.isInvAPICall);
        //RecursiveHandler.isInvAPICall = TRUE;
        if(callInvAPI && RecursiveHandler.isInvAPICall)
        {
            if(invForAPI1.size()>0){
                RecursiveHandler.isInvAPICall = false;
                InvestmentRestAPIController.sendInvestmentDetails(invForAPI1,false);
            }
            if(invForAPI2.size()>0){
                RecursiveHandler.isInvAPICall = false;
                InvestmentRestAPIController.sendInvestmentDetails(invForAPI2,false);
            }
            if(invDeleteIdsAPI1.size()>0){
                RecursiveHandler.isInvAPICall = false;
                RestLoginController.genericBulkDeleteAPI(invDeleteIdsAPI1,'salesforce_investment_id','startups/startupInvestment/deleteStartupInvestmentUsingSalesforceId');
            }
            if(invDeleteIdsAPI2.size()>0){
                RecursiveHandler.isInvAPICall = false;
                RestLoginController.genericBulkDeleteAPI(invDeleteIdsAPI2,'salesforce_investment_id','startups/startupInvestment/deleteStartupInvestmentUsingSalesforceId');
            }
            
        }
        
        //Call WhatsApp API
        if(!Test.isRunningTest() && RecursiveHandler.isInvWhatsAppAPICall && callWhatsappAPI)
        {
            if(invWhatsAppAPITypeMap.size()>0)
            {
                String jsonData = JSON.serialize(invWhatsAppAPITypeMap);
                OutboundMessageAPIController.sendWhatsappAPIInv(jsonData);
                //OutboundMessageAPIController.sendWhatsappAPIInv(invWhatsAppAPITypeMap);
            }
            if(invWhatsAppAPIFeeChequeMap.size()>0)
            {
                String jsonData = JSON.serialize(invWhatsAppAPIFeeChequeMap);
                OutboundMessageAPIController.sendWhatsappAPIInv(jsonData);
                // OutboundMessageAPIController.sendWhatsappAPIInv(invWhatsAppAPIFeeChequeMap);
            }
            
            RecursiveHandler.isInvWhatsAppAPICall =false;
        }
        
        
        system.debug('afterUpdate accInvMap>>>>>'+accInvMap); 
        system.debug('afterUpdate contInvSet>>>>>'+contInvSet); 
        if(accInvMap!=null && accInvMap.size()>0)
        {
            //Temporary Commented by Hemant || Date: 11-05-2024 || Getting "You can not edit this field because these fields are locked right now" Error for bulk update and exit module and getting APEX CPU time limits and platform limitations
            //updateStartupInvRollup(accInvMap);
        }
        
        if(contInvSet!=null && contInvSet.size()>0)
        {
            updateContactInvRollup(contInvSet);
        }
        
        if(invertorAmountMap.size()>0)
        {
            // createTransactionObj(invertorAmountMap); 
        }
        
        if(invForUserActivity.size() > 0)
        {
            handleInvestmentActivity(invForUserActivity,OldInvmap);     
        }

        if(invForPointTransaction.size() > 0)
        {
            createPointTransaction(invForPointTransaction);
        }
        //Added by jay dabhi for Referral task creation on trigger  on 11/08/2025
        if(!invList.Isempty()){
        ReferralTaskCreation.taskCreationOnInvestmentUpdate(invList, OldInvmap);
        System.Debug('Referral Task Called >>');
         }
        set<Id> Invids = new set<Id>();
        for (Investment__c inv : invList) {
         Invids.add(inv.Id);
          }
         ReferralTaskCreation.checkMilestoneCrossing(Invids);
    }
    
    public void afterDelete(List<Investment__c> invList)
    {
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('Contact trigger API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Investment_API_Call__c)
        {
            set<ID> idsToDelete1 = new set<ID>();
            set<ID> idsToDelete2 = new set<ID>();
            Integer count = 0;
            
            //Added by Karan 19/09/2022:
            Set<id> StrtRundIds = new set<id>();
            for(Investment__c inv : invList)
            {
                StrtRundIds.add(inv.Startup_Round__c);
            }
            system.debug('After Delete StrtRundIds Ids>>>>>>>>'+StrtRundIds.size());
            List<Startup_Round__c> strtupListTobalanceOfShare = [Select Id, Balance_Of_Share__c,No_of_Investment__c,(Select Id,Startup_Round__c From Investments__r) From Startup_Round__c Where Id IN: StrtRundIds];
            for(Startup_Round__c strtupR: strtupListTobalanceOfShare){
                strtupR.No_of_Investment__c =  strtupR.Investments__r.size();
            }
            update strtupListTobalanceOfShare;
            
            //Added by karan for balance of sale 30/06/2022.
            for(Investment__c inv : invList)
            {
                if(inv.Startup_Round__c!=null)
                {
                    strtupInvIDs.add(inv.Startup_Round__c);
                }
            }
            system.debug('Delete investment Iddddd>>>'+strtupInvIDs);
            aggregateResults();
            
            for(investment__C inv : invList)
            {
                if(inv.Startup_Round__c!=null)
                {
                    StrtupRoundIds.add(inv.Startup_Round__c);
                }
            }
            startupRoundRollup();
            
            
            for(Investment__c inv : invList)
            {
                if(count < 100)
                    idsToDelete1.add(inv.Id);
                else
                    idsToDelete2.add(inv.Id);
                
                count ++;   
            }
            if(idsToDelete1.size()>0){
                RestLoginController.genericBulkDeleteAPI(idsToDelete1,'salesforce_investment_id','startups/startupInvestment/deleteStartupInvestmentUsingSalesforceId');
            }
            if(idsToDelete2.size()>0){
                RestLoginController.genericBulkDeleteAPI(idsToDelete2,'salesforce_investment_id','startups/startupInvestment/deleteStartupInvestmentUsingSalesforceId');
            }
        }
    }
   
    // Method Updated by Karan on 05/09/2024 add null checks for map and startuprounds:
    public void createPointTransaction(List<Investment__c> invList){
        system.debug('calledddd');
        set<Id> invId = new set<ID>();
        map<ID,Investment__c> mapInvId = new map<ID,Investment__c>();
        for(Investment__c inv : invList){
            if (inv != null) {
            invId.add(inv.Id);
        }
        }
        List<Points_Transaction__c> pointsList = [SELECT Id FROM Points_Transaction__c WHERE investment__c IN :invId 
                                                 and Point_Type__c = 'Invest 5L and above'];
        List<Points_Transaction__c> pointsBackOutList = [SELECT Id FROM Points_Transaction__c WHERE investment__c IN :invId 
                                                 and Point_Type__c = 'Backed Out'];
        List<Investment__c> invSRList = [SELECT Id,Startup_Round__r.startup__c,Startup_Round__c,Startup_Round__r.Round_Type__c , Startup_Round__r.Date_of_Investor_Call__c from Investment__c
                                         WHERE ID IN : invId AND (Startup_Round__r.Round_Type__c = 'Internal Transfers' OR Startup_Round__r.Round_Type__c = 'Raise'
                                                                 Or Startup_Round__r.Round_Type__c = 'Pre-Emptive')];
        
        if(!invSRList.isEmpty()){
            for(Investment__c inv : invSRList){
                mapInvId.put(inv.Id,inv);
            }
        }
        System.debug(' mapInvId >>>> ' + mapInvId);
        // Updated by Karan for, for loop if conditions with null checks on 05/09/2024;
        List<Points_Transaction__c> points = new List<Points_Transaction__c>();
        for(Investment__c inv : invList){  
          //  Investment__c oldValuesOnThatInvestment = oldMapInvestment.get(inv.id);
    //         if (oldValuesOnThatInvestment.Type__c != inv.Type__c && 
    // ((oldValuesOnThatInvestment.Type__c != 'Committed' && inv.Type__c == 'Committed') || 
    //  (oldValuesOnThatInvestment.Type__c != 'Invested' && inv.Type__c == 'Invested'))) {
 
            if(inv != null && inv.Account__c != null){  
                if(!RecursiveHandlerForPointTransaction.invIdsFor5LPoints.contains(inv.Id))
                {
                    system.debug('calledddd' +inv.Account__c);
                    if(mapInvId != null && mapInvId.get(inv.Id) != null && mapInvId.get(inv.Id).Startup_Round__r != null 
                        && mapInvId.get(inv.Id).Startup_Round__r.Round_Type__c != null) {
                        if(pointsList.isEmpty()){
                            if(inv.Investment_Amount__c != null && inv.Investment_Amount__c >= 490000){
                            system.debug('calledddd 11' +inv.Account__c);
                            if((inv.Type__c == 'Invested' && (mapInvId.get(inv.Id).Startup_Round__r.Round_Type__c == 'Internal Transfers' 
                                                            || mapInvId.get(inv.Id).Startup_Round__r.Round_Type__c == 'Pre-Emptive'))
                            || (inv.Parent_Investment__c == null && mapInvId.get(inv.Id).Startup_Round__r.Round_Type__c == 'Raise')){
                                system.debug('calledddd 22' +inv.Account__c);
                                Points_Transaction__c point =  new Points_Transaction__c();
                                point.Credit_To__c = inv.Account__c;
                                point.Debit_From__c = Label.PT_Debit_Account;  
                                system.debug('Debit_From__c>>>>>>' +point.Debit_From__c);
                                point.Points_Alloted__c = 50;
                                point.Point_Type__c = 'Invest 5L and above';
                                //point.date__c = system.today();  commented by sahil on 19.09.2024
                                point.Startup_Round__c = inv.Startup_Round__c;
                                point.Investor__c = inv.Investor__c;
                                point.Startup__c = mapInvId.get(inv.Id).Startup_Round__r.Startup__c;
                                if (inv.Startup_Round__r != null && inv.Startup_Round__r.Startup__c != null) {
                                point.Startup__c = inv.Startup_Round__r.Startup__c;
                                    }
                                point.Member_Name__c = inv.Account__c;
                                point.Investment_Date__c = system.today();
                                //point.investment__c = inv.id;  commented by sahil on 19.09.2024
                                point.Investment_Typ__c = inv.Type__c;
                                //point.Date_of_IC__c = system.today();  commented by sahil on 19.09.2024
                                points.add(point);
                                RecursiveHandlerForPointTransaction.invIdsFor5LPoints.add(inv.Id);

                            }                    
                        } 
                        }
                        
                    }
                }

                if(!RecursiveHandlerForPointTransaction.invIdsForBackoutPoints.contains(inv.Id))
                {
                    if(pointsBackOutList.isEmpty()){
                        if(inv.Type__c == 'Back out unapproved'){
                            system.debug('calledddd 11' +inv.Type__c);
                            Points_Transaction__c point =  new Points_Transaction__c();
                            point.Credit_To__c = Label.PT_Debit_Account;  
                            point.Debit_From__c = inv.Account__c;
                            point.Points_Alloted__c = -200;
                            point.Point_Type__c = 'Backed Out';
                            //point.date__c = system.today();  commented by sahil on 19.09.2024
                            //  added by sahil on 19.06.2024 as discussed with Mauli Ma'am
                            point.Date__c = mapInvId.get(inv.Id).Startup_Round__r.Date_of_Investor_Call__c;
                            point.Startup_Round__c = inv.Startup_Round__c;
                            point.Investor__c = inv.Investor__c;
                            //point.investment__c = inv.id;  commented by sahil on 19.09.2024
                            point.Startup__c = mapInvId.get(inv.Id).Startup_Round__r.Startup__c;
                            if (inv.Startup_Round__r != null && inv.Startup_Round__r.Startup__c != null) {
                            //point.Startup__c = inv.Startup_Round__r.Startup__c;
                                }
                            point.Member_Name__c = inv.Account__c;
                            point.Investment_Typ__c = inv.Type__c;
                            //point.Investment_Date__c = system.today();  commented by sahil on 19.09.2024
                            //point.Date_of_IC__c = system.today();  commented by sahil on 19.09.2024
                            //  added by sahil on 19.06.2024 as discussed with Mauli Ma'am
                            point.Date_of_IC__c = mapInvId.get(inv.Id).Startup_Round__r.Date_of_Investor_Call__c;
                            System.debug('DATEE >>>>>>> ' + mapInvId.get(inv.Id).Startup_Round__r.Date_of_Investor_Call__c);
                            points.add(point);
                            RecursiveHandlerForPointTransaction.invIdsForBackoutPoints.add(inv.Id);
                        }
                    }  
                }              
            }            
        }            
    //}
        system.debug('Points ::'+points);   
    if(!points.isEmpty()){
        insert points;
        // isPointTransactionCreationAllowed = true;
    }       

    System.debug('ID Set Of The 5L Points >>>>>> ' + RecursiveHandlerForPointTransaction.invIdsFor5LPoints); 
    System.debug('ID Set Of The Backout Points >>>>>> ' + RecursiveHandlerForPointTransaction.invIdsForBackoutPoints); 
}
    // Changes were made by ankush for AIF Total commitment.
    public void updateContactInvRollup(Set<Id> contInvPrmSet)
    {
        system.debug('contInvPrmSet>>>>>'+contInvPrmSet);
        Map<Id,Decimal> contAIFInvestedMap = New Map<Id,Decimal>();
        Map<Id,Decimal> contAIFCommittedMap = New Map<Id,Decimal>();
        
        for(Investment__c inv : [select id,Investment_Amount__c,Final_Commitment_Amount__c,Type__c,Investor__c,Investor_Type__c from Investment__c where Investor__c in : contInvPrmSet])
        {
            if(inv.Investor_Type__c == 'Via AIF')
            {
                if(inv.Investment_Amount__c==null)
                    inv.Investment_Amount__c = 0;
                if(inv.Final_Commitment_Amount__c==null)
                    inv.Final_Commitment_Amount__c = 0;
                
                if(inv.Type__c == 'Invested')
                {
                    if(!contAIFInvestedMap.containsKey(inv.Investor__c))
                        contAIFInvestedMap.put(inv.Investor__c,0);
                    
                    contAIFInvestedMap.put(inv.Investor__c,contAIFInvestedMap.get(inv.Investor__c)+inv.Investment_Amount__c);                 
                }
                else if(inv.Type__c == 'Committed')
                {
                    if(!contAIFCommittedMap.containsKey(inv.Investor__c))
                        contAIFCommittedMap.put(inv.Investor__c,0);
                    
                    contAIFCommittedMap.put(inv.Investor__c,contAIFCommittedMap.get(inv.Investor__c)+inv.Final_Commitment_Amount__c);  
                }
            }
        }
        
        if(contAIFInvestedMap.size()>0 || contAIFCommittedMap.size()>0)
        {
            List<Contact> updateContactList = New List<Contact>();
            
            for(Contact c : [select id,Total_Commitment_made_via_AIF__c,Total_Investment_made_via_AIF__c from Contact where id in : contAIFInvestedMap.Keyset() OR Id in : contAIFCommittedMap.keyset()])
            {
                if(contAIFInvestedMap.containsKey(c.Id)){
                    c.Total_Investment_made_via_AIF__c = contAIFInvestedMap.get(c.Id);
                }
                
                if(contAIFCommittedMap.containsKey(c.Id)){
                    c.Total_Commitment_made_via_AIF__c = contAIFCommittedMap.get(c.Id);
                    
                }
                updateContactList.add(c);
            }
            
            if(updateContactList!=null && updateContactList.size()>0)
                update updateContactList;
        }
    }
    /*
    //Temporary Commented by Hemant || Date: 11-05-2024 || Getting "You can not edit this field because these fields are locked right now" Error for bulk update and exit module and getting APEX CPU time limits and platform limitations
    public void updateStartupInvRollup(Map<Id,List<Investment__c>> accInvMap)
    {
        List<Investment__c> insertInvList = new List<Investment__c>();
        List<String> tempStartUpList = new List<String>();
        Map<Id,Set<String>> accBOACountMap = new Map<Id,Set<String>>();
        Map<Id,Set<String>> accWaitListStartupMap = new Map<Id,Set<String>>();
        Map<Id,Set<Id>> accInvStartupMap = new Map<Id,Set<ID>>();
        Map<Id,Set<String>> accSAFCopyStartupMap = new Map<Id,Set<String>>();
        Map<Id,Set<String>> accFnFStartupMap = new Map<Id,Set<String>>();
        Set<String> issueTypeValueSet = new Set<String>{'Friends & Family T1','Friends & Family T2','Friends & Family T1 - AIF','Friends & Family T2 - AIF'};
            
            //for(Investment__c inv : [select id,Type__c,Account__c,Startup_Name__c,Startup_Round__r.Startup_Name__c,Total_Back_Outs_Unapproved__c,Recent_Back_outs__c,Recent_Waitlist__c,Startup_Round__c,SAF_soft_copy__c,Transfer_Deed_hard_copy__c,Deed_of_Adherence_hard_copy__c,DOA_stamp_paper_hard_copy__c,FnF_Documents_pending__c,Issue_Type__c from Investment__c where Account__c in : accInvMap.keyset() AND (Type__c = 'Back out unapproved' OR Type__c ='Waitlist' OR Type__c='Invested') order by Startup_Round__r.Date_of_Investor_Call__c desc])
        for(Investment__c inv : [select id,Type__c,Account__c,Startup_Name__c,Startup_Round__r.Startup_Name__c,Total_Back_Outs_Unapproved__c,Recent_Back_outs__c,Recent_Waitlist__c,Startup_Round__c,SAF_soft_copy__c,Transfer_Deed_hard_copy__c,Deed_of_Adherence_hard_copy__c,DOA_stamp_paper_hard_copy__c,FnF_Documents_pending__c,Issue_Type__c from Investment__c where Account__c in : accInvMap.keyset() order by Startup_Round__r.Date_of_Investor_Call__c desc])
        {
            RecursiveHandler.accountIdForRollUp.add(inv.Account__c);
            
            if(inv.Type__c =='Back out unapproved') 
            {
                if(!accBOACountMap.containsKey(inv.Account__c))
                    accBOACountMap.put(inv.Account__c,new Set<String>());
                
                accBOACountMap.get(inv.Account__c).add(inv.Startup_Round__r.Startup_Name__c);  
            }
            else if(inv.Type__c =='Waitlist')
            {
                if(!accWaitListStartupMap.containsKey(inv.Account__c))
                    accWaitListStartupMap.put(inv.Account__c,new Set<String>());
                
                accWaitListStartupMap.get(inv.Account__c).add(inv.Startup_Round__r.Startup_Name__c);   
            }
            else if(inv.Type__c =='Invested')
            {
                if(!accInvStartupMap.containsKey(inv.Account__c))
                    accInvStartupMap.put(inv.Account__c,new Set<ID>());
                
                accInvStartupMap.get(inv.Account__c).add(inv.Startup_Round__c);   
            }
            
            if(!inv.SAF_soft_copy__c && inv.Type__c =='Invested' && inv.Issue_Type__c=='Primary')
            {
                if(!accSAFCopyStartupMap.containsKey(inv.Account__c))
                    accSAFCopyStartupMap.put(inv.Account__c,new Set<String>());
                
                accSAFCopyStartupMap.get(inv.Account__c).add(inv.Startup_Round__r.Startup_Name__c);  
            }
            if(inv.Type__c =='Invested' && issueTypeValueSet.contains(inv.Issue_Type__c) && (inv.Transfer_Deed_hard_copy__c==false || inv.Deed_of_Adherence_hard_copy__c==false || inv.DOA_stamp_paper_hard_copy__c==false))
            {
                system.debug('inv>>>'+inv);
                if(!accFnFStartupMap.containsKey(inv.Account__c))
                    accFnFStartupMap.put(inv.Account__c,new Set<String>());
                
                accFnFStartupMap.get(inv.Account__c).add(inv.Startup_Round__r.Startup_Name__c);  
            }
            accInvMap.get(inv.Account__c).add(inv);              
        }
        
        system.debug('accInvMap111>>>>>'+accInvMap); 
        system.debug('accBOACountMap111>>>>>'+accBOACountMap); 
        system.debug('accFnFStartupMap111>>>>>'+accFnFStartupMap); 
        
        for(Id key : accInvMap.keyset())
        {
            for(Investment__c inv : accInvMap.get(key))
            {
                system.debug('inv>>>>>'+inv); 
                if(accBOACountMap!=null && accBOACountMap.containsKey(inv.Account__c) && accBOACountMap.get(inv.Account__c).size()>0)
                {
                    tempStartUpList = new List<String>();
                    tempStartUpList.addAll(accBOACountMap.get(inv.Account__c));
                    inv.Total_Back_Outs_Unapproved__c = string.join(tempStartUpList,',');//accBOACountMap.get(inv.Account__c).toString();
                    
                    if(tempStartUpList.size()>0)
                    {
                        inv.Recent_Back_outs__c  = getRecentStartup(tempStartUpList,2);
                    }
                }
                else
                {
                    inv.Total_Back_Outs_Unapproved__c = '';
                    inv.Recent_Back_outs__c = '';
                }
                if(accWaitListStartupMap!=null && accWaitListStartupMap.containsKey(inv.Account__c) && accWaitListStartupMap.get(inv.Account__c).size()>0)
                {
                    tempStartUpList = new List<String>();
                    tempStartUpList.addAll(accWaitListStartupMap.get(inv.Account__c));
                    inv.Recent_Waitlist__c = getRecentStartup(tempStartUpList,2);              
                }
                else
                {
                    inv.Recent_Waitlist__c = '';
                }
                if(accInvStartupMap!=null && accInvStartupMap.containsKey(inv.Account__c) && accInvStartupMap.get(inv.Account__c).size()>0)
                {
                    inv.Total_Investments__c = accInvStartupMap.get(inv.Account__c).size();
                }
                else
                {
                    inv.Total_Investments__c = 0;
                }
                if(accSAFCopyStartupMap!=null && accSAFCopyStartupMap.containsKey(inv.Account__c) && accSAFCopyStartupMap.get(inv.Account__c).size()>0)
                {
                    tempStartUpList = new List<String>();
                    tempStartUpList.addAll(accSAFCopyStartupMap.get(inv.Account__c));
                    inv.SAF_pending__c = string.join(tempStartUpList,','); 
                }
                else
                {
                    inv.SAF_pending__c='';
                }
                
                if(accFnFStartupMap!=null && accFnFStartupMap.containsKey(inv.Account__c) && accFnFStartupMap.get(inv.Account__c).size()>0)
                {
                    tempStartUpList = new List<String>();
                    tempStartUpList.addAll(accFnFStartupMap.get(inv.Account__c));
                    inv.FnF_Documents_pending__c = string.join(tempStartUpList,','); 
                }
                else
                {
                    inv.FnF_Documents_pending__c ='';
                }
                
                insertInvList.add(inv);
            }   
        }
        system.debug('insertInvList>>>>>'+insertInvList); 
        if(insertInvList!=null && insertInvList.size()>0)
            update insertInvList;
    }
    */
    /*
    // Commented by Sahil on 07.01.2025 This is method not getting called. (calling this method was already commented and this method is not getting used)
    public String getRecentStartup(List<String> startupSet,Integer noOfRec)
    {
        String tempReturnStr = '';
        if(startupSet.size()>0)
        {
            for(Integer i = 0;i<noOfRec;i++)
            {
                if(i<startupSet.size())
                    tempReturnStr = tempReturnStr+startupSet[i]+','; 
            }
            If(String.isNotBlank(tempReturnStr))
                tempReturnStr = tempReturnStr.removeEnd(',');
        }
        return tempReturnStr;
    }*/
    
    //Added by Karan to Sum the field values and update on Startup Round 30/06/2022.
    public void aggregateResults()
    { 
        
        List<Investment__c> sumInvList = new List<Investment__c>([SELECT id, Name, Number_Of_Shares__c, Startup_Round__c, Intransfer_balance_share__c, Invested_number_of_share__c, IPVHQ_Shadow_Balance_Share__c, IPVFnF_Shadow_Balance_share__c, Family_Balance_Share__c,Startup_Round__r.No_of_Investment__c from Investment__c Where (Intransfer_balance_share__c = true OR Invested_number_of_share__c = true OR IPVHQ_Shadow_Balance_Share__c = true OR IPVFnF_Shadow_Balance_share__c = true OR Family_Balance_Share__c = true) AND (Startup_Round__c IN :strtupInvIDs)]);
        Map<Id,Boolean> internalTransferFoundMap = new Map<Id,Boolean>();
        Map<Id,List<Investment__c>> strtupInvMap = new Map<Id,List<Investment__c>>();
        List<Startup_Round__c> listtotupdate = new List<Startup_Round__c>();
        
        for(Investment__c Inv : sumInvList)
        {
            //system.debug('ID>>>>>>>>'+Inv.Id);
            //system.debug('Inv.Startup_Round__r.No_of_Investment__c>>>>>>>>'+Inv.Startup_Round__r.No_of_Investment__c);
            if(!strtupInvMap.containsKey(Inv.Startup_Round__c))
            {
                strtupInvMap.put(Inv.Startup_Round__c,New List<Investment__c>());
            }
            
            strtupInvMap.get(Inv.Startup_Round__c).add(Inv);
            if(inv.Intransfer_balance_share__c)
                internalTransferFoundMap.put(Inv.Startup_Round__c,inv.Intransfer_balance_share__c);
        }
        
        system.debug('strtupInvMap>>>>>>>>'+strtupInvMap);
        for(Id key : strtupInvMap.keyset()){
            integer sum1 = 0;
            integer sum2 = 0;
            integer sum3 = 0;
            integer sum4 = 0;
            integer sum5 = 0;
            for(Investment__c inv : strtupInvMap.get(key)){
                if(inv.Intransfer_balance_share__c == true && inv.Number_Of_Shares__c != null)
                {
                    sum1 += integer.valueOf(inv.Number_Of_Shares__c);
                }
                if(inv.Invested_number_of_share__c == true && inv.Number_Of_Shares__c != null)
                {
                    sum2 += integer.valueOf(inv.Number_Of_Shares__c);
                }
                if(inv.IPVHQ_Shadow_Balance_Share__c == true && inv.Number_Of_Shares__c != null)
                {
                    //System.debug('Sum3>>>>>>>>'+sum3);
                    sum3 += integer.valueOf(inv.Number_Of_Shares__c);
                }
                if(inv.IPVFnF_Shadow_Balance_share__c == true && inv.Number_Of_Shares__c != null)
                {
                    //System.debug('Sum3>>>>>>>>'+sum3);
                    sum4 += integer.valueOf(inv.Number_Of_Shares__c);
                }
                if(inv.Family_Balance_Share__c == true && inv.Number_Of_Shares__c != null)
                {
                    sum5 += integer.valueOf(inv.Number_Of_Shares__c);
                }
            }
            
            Startup_Round__c stround = new Startup_Round__c();
            stround.Intransfer_balance_share__c = sum1;
            stround.Invested_Balance_of_share__c = sum2;
            //System.debug('stroundInvested231>>>>>>>>'+stround.Invested_Balance_of_share__c);
            stround.IPVHQ_Shadow_Balance_Share__c = sum3;
            //System.debug('stround>>>>>>>>'+stround.IPVHQ_Shadow_Balance_Share__c);
            stround.IPVFnF_Shadow_Balance_share__c = sum4;
            //System.debug('stroundIPVFnF>>>>>>>>'+stround.IPVFnF_Shadow_Balance_share__c);
            stround.Family_Balance_Share__c = sum5;
            //System.debug('stroundFamilysh>>>>>>>>'+stround.Family_Balance_Share__c);
            stround.Id = key;
            
            //Add below for loop call to update balance of share on startup round
            //Internal transfer scenario
            
            if(internalTransferFoundMap!=null && internalTransferFoundMap.containsKey(stround.Id) && internalTransferFoundMap.get(stround.Id)){
                system.debug('Balance of share 555'+stround.Balance_Of_Share__c);
                stround.Balance_Of_Share__c = stround.Invested_Balance_of_share__c - (stround.IPVHQ_Shadow_Balance_Share__c + stround.IPVFnF_Shadow_Balance_share__c); 
                system.debug('Balance of share 666'+stround.Balance_Of_Share__c);
            }//NON- Internal transfer scenario
            else
            {
                system.debug('Balance of share 888'+stround.Balance_Of_Share__c);
                stround.Balance_Of_Share__c = (stround.Invested_Balance_of_share__c - (stround.IPVHQ_Shadow_Balance_Share__c + stround.IPVFnF_Shadow_Balance_share__c) - stround.Family_Balance_Share__c); 
                system.debug('Balance of share 999'+stround.Balance_Of_Share__c);
            }
            if(stround.Balance_Of_Share__c<0)
                stround.Balance_Of_Share__c = 0;
            listtotupdate.add(stround);
        }
        
        for(Id strId : strtupInvIDs)
        {
            if(!strtupInvMap.containsKey(strId))
            {
                Startup_Round__c stround = new Startup_Round__c();
                stround.Intransfer_balance_share__c = 0;
                stround.Invested_Balance_of_share__c = 0;
                stround.IPVHQ_Shadow_Balance_Share__c = 0;
                stround.IPVFnF_Shadow_Balance_share__c = 0;
                stround.Family_Balance_Share__c = 0;
                stround.Balance_Of_Share__c = 0;
                stround.Id = strId;
                listtotupdate.add(stround);
            }
        }
        system.debug('listtotupdate>>>>>>'+listtotupdate);
        if(listtotupdate!=null && listtotupdate.size()>0)     
            update listtotupdate;
        for(Startup_Round__c sr : listtotupdate){
            system.debug('Balance of share4AFTER UPDATE'+sr.Balance_Of_Share__c);
        }
    }
    //Added by Ankush For Deal traker 25/8/2022
    public void startupRoundRollup(){
        if(StrtupRoundIds != null && StrtupRoundIds.size() >0)
        {
        for(Id StrIds:StrtupRoundIds)
            {
                StrRoundRecords.put(StrIds,new Startup_round__C(id=StrIds,FnF_Expected_Amount__c=0, FnF_Backout_Amount__c=0, AIF_CFM_Sent_Amount__c=0, AIF_Expected_Amount__c=0, FnF_Amount_of_CFM_Sent__c=0, FnF_Amount_received__c =0, PLT_CFM_Yet_to_Go_Amount__c =0,PLT_CFM_Sent_Amount__c=0,FNF_HQ_Shadow_Shares__c=0));
                //System.debug('StrMap>>>' + StrRoundRecords);
            }
        
        for(Investment__c StrInvlist:[Select Id, Startup_round__c,Investor_Type__c, Date_of_FnF_CFM__c,call_for_money_sent__c,Investment_Amount__c,Number_Of_Shares__c,Investment_Amount_Due__c, Startup_round__r.PLT_CFM_Sent_Amount__c,Startup_round__r.PLT_CFM_Yet_to_Go_Amount__c,
                                      Issue_Type__c, Type__c,Startup_round__r.AIF_Expected_Amount__c,Startup_round__r.FnF_Expected_Amount__c,Startup_round__r.FnF_Amount_received__c,Startup_round__r.FnF_Amount_of_CFM_Sent__c,
                                      Startup_round__r.AIF_CFM_Sent_Amount__c,Startup_round__r.FnF_Backout_Amount__c,Startup_round__r.FNF_HQ_Shadow_Shares__c,Membership_Validity__c from Investment__c 
                                      where Startup_round__c in:StrtupRoundIds])
            {
                if(StrInvlist.Number_Of_Shares__c != null && StrInvlist.Issue_Type__c =='IPV HQ - Shadow' && (StrInvlist.Type__c =='Invested - Shadow' || StrInvlist.Type__c =='Committed'))
                    {
                        StrRoundRecords.get(StrInvlist.Startup_round__c).FNF_HQ_Shadow_Shares__c += StrInvlist.Number_Of_Shares__c;
                    }
            
                if(StrInvlist.Type__c =='Invested' || StrInvlist.Type__c =='Committed')
                    {
                
                        if(StrInvlist.Investment_Amount__c!= null && (StrInvlist.issue_type__c =='Friends & Family T1' || StrInvlist.Issue_Type__c =='Friends & Family T2' || StrInvlist.Issue_type__c =='IPV FnF - Shadow' || StrInvlist.Issue_type__c =='IPV HQ - Shadow'))
                            StrRoundRecords.get(StrInvlist.Startup_round__c).FnF_Amount_received__c += StrInvlist.Investment_Amount__c;
                
                        if(StrInvlist.Investment_Amount_Due__c !=null && StrInvlist.Issue_type__c =='Primary' && StrInvList.Call_For_Money_Sent__c == true)
                            StrRoundRecords.get(StrInvlist.Startup_round__c).PLT_CFM_Sent_Amount__c += StrInvlist.Investment_Amount_Due__c;
                
                        if(StrInvlist.Investment_Amount_Due__c !=null && StrInvlist.Issue_type__c =='Primary' && StrInvList.Call_For_Money_Sent__c == false)
                            StrRoundRecords.get(StrInvlist.Startup_round__c).PLT_CFM_Yet_to_Go_Amount__c += StrInvlist.Investment_Amount_Due__c;
                
                        if((StrInvlist.issue_type__c =='Friends & Family T1' || StrInvlist.Issue_Type__c =='Friends & Family T2' || StrInvlist.Issue_type__c =='IPV FnF - Shadow') && StrInvlist.Investment_Amount_Due__c !=null)
                         {    
                            StrRoundRecords.get(StrInvlist.Startup_round__c).FnF_Expected_Amount__c +=StrInvlist.Investment_Amount_Due__c;
                         }
                
                        if(StrInvlist.Date_of_FnF_CFM__c != null && StrInvlist.Investment_Amount_Due__c !=null)
                         {
                            StrRoundRecords.get(StrInvlist.Startup_round__c).FnF_Amount_of_CFM_Sent__c += StrInvlist.Investment_Amount_Due__c;
                         }
                        if(StrInvList.Call_For_Money_Sent__c){
                            if(StrInvlist.Investment_Amount_Due__c !=null && StrInvlist.call_for_money_sent__c == true && (StrInvlist.Issue_type__c=='Primary - AIF' || StrInvlist.issue_type__c =='Friends & Family T1 - AIF' || StrInvlist.Issue_type__c =='Friends & Family T2 - AIF')){
                                StrRoundRecords.get(StrInvlist.Startup_round__c).AIF_CFM_Sent_Amount__c += StrInvlist.Investment_Amount_Due__c;
                            } }
                        if((StrInvlist.Issue_type__c=='Primary - AIF' || StrInvlist.issue_type__c =='Friends & Family T1 - AIF' || StrInvlist.Issue_type__c =='Friends & Family T2 - AIF') && StrInvlist.Investment_Amount_Due__c !=null)
                         {
                             StrRoundRecords.get(StrInvlist.Startup_round__c).AIF_Expected_Amount__c += StrInvlist.Investment_Amount_Due__c;
                         }
                    }
            
                if(StrInvlist.Investment_Amount_Due__c !=null && (StrInvlist.Type__c =='Back out approved' || StrInvlist.Type__c =='Back out unapproved'))
                    StrRoundRecords.get(StrInvlist.Startup_round__c).FnF_Backout_Amount__c += StrInvlist.Investment_Amount_Due__c;
            }
            update(StrRoundRecords.values());
            system.debug('updatedRollupMap>>>'+ strRoundRecords.values());
    }   }
    
    // Added by Jay Dabhi on 6th September 2024 for handling user activity based on Investment changes
  public void handleInvestmentActivity(List<Investment__c> invList1,Map<Id,Investment__c>OldInvmap) {
    List<User_Activity__c> userActivityRecords = new List<User_Activity__c>();
    Id recordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByName().get('IPV').getRecordTypeId();
    
        invList1 = [SELECT Id, Type__c, Investor_Name__c, Account__c, Name,Date_of_transaction__c,
                      Startup_Round__r.Name 
               FROM Investment__c 
               WHERE Id IN :invList1];

    for (Investment__c invRecord : invList1) {
        Investment__c oldInv = OldInvMap != null ? OldInvMap.get(invRecord.Id) : null;

        
        if (invRecord.Type__c == 'Committed' && 
            (oldInv == null || invRecord.Type__c != oldInv.Type__c) && 
            !RecursiveHandlerForPointTransaction.invIdsForCommittedActivity.contains(invRecord.Id)) {

            String investorUrl = URL.getOrgDomainURL().toExternalForm() + '/' + invRecord.Investor_Name__c;
            String startupRoundUrl = URL.getOrgDomainURL().toExternalForm() + '/' + invRecord.Startup_Round__c;
            String invUrl = URL.getOrgDomainURL().toExternalForm() + '/' + invRecord.Id;

            User_Activity__c activity = new User_Activity__c();
            activity.Related_Account__c = invRecord.Account__c;
            activity.Activity_Type__c = 'Commitment Done';
            activity.Activity_Detail_RICH__c =
                (investorUrl != '' ? '<a href="' + investorUrl + '" target="_blank">' + invRecord.Investor_Name__c + '</a>' : invRecord.Investor_Name__c) +
                ' Committed in ' +
                (startupRoundUrl != '' ? '<a href="' + startupRoundUrl + '" target="_blank">' + invRecord.Startup_Round__r.Name + '</a>' : invRecord.Startup_Round__r.Name) +
                ' ( ' + (invUrl != '' ? '<a href="' + invUrl + '" target="_blank">' + invRecord.Name + '</a>' : invRecord.Name) + ' )';
            activity.Time_Stamp__c = System.now();
            userActivityRecords.add(activity);
            RecursiveHandlerForPointTransaction.invIdsForCommittedActivity.add(invRecord.Id);
        }

      
        if (invRecord.Type__c == 'Invested' &&
            (oldInv == null || invRecord.Type__c != oldInv.Type__c) &&
            !RecursiveHandlerForPointTransaction.invIdsForInvestmentActivity.contains(invRecord.Id)) {

            String investorUrl = URL.getOrgDomainURL().toExternalForm() + '/' + invRecord.Investor_Name__c;
            String startupRoundUrl = (invRecord.Startup_Round__r != null) ? URL.getOrgDomainURL().toExternalForm() + '/' + invRecord.Startup_Round__r.Name : '';
            String invUrl = URL.getOrgDomainURL().toExternalForm() + '/' + invRecord.Id;

            User_Activity__c activity = new User_Activity__c();
            activity.Related_Account__c = invRecord.Account__c;
            activity.Activity_Type__c = 'Investment Done';
            activity.Activity_Detail_RICH__c =
                (investorUrl != '' ? '<a href="' + investorUrl + '" target="_blank">' + invRecord.Investor_Name__c + '</a>' : invRecord.Investor_Name__c) +
                ' Invested in ' +
                (startupRoundUrl != '' ? '<a href="' + startupRoundUrl + '" target="_blank">' + invRecord.Startup_Round__r.Name + '</a>' : invRecord.Startup_Round__r.Name) +
                ' ( ' + (invUrl != '' ? '<a href="' + invUrl + '" target="_blank">' + invRecord.Name + '</a>' : invRecord.Name) + ' )';
            activity.Time_Stamp__c = invRecord.Date_of_transaction__c;
            userActivityRecords.add(activity);
            RecursiveHandlerForPointTransaction.invIdsForInvestmentActivity.add(invRecord.Id);
        }
    }

   
    if (!userActivityRecords.isEmpty()) {
        insert userActivityRecords;
        System.debug('Inserted User Activities: ' + userActivityRecords);
    }
}
    
    /*

IRR Formula:
_____________________________________________________________________________
x^y = pow(x, y)

IRRCal = Math.pow(sharCal ,periodCal);

sharCal^periodCal


if(numberMonthDiff >=6)
periodCal = 1/ 365/numberDaysDue;;
else
periodCal = 365/numberDaysDue;

if(numberMonthDiff >=6)
(Exit price /Issue price) ^ (1/365/numberDaysDue)
else    
(Exit price/Issue Price) ^ (365/numberDaysDue)

_____________________________________________________________________________
Public void createTransactionObj(Map<Id,Decimal> invertorAmountMap){
system.debug('invertorAmountMap>>>'+ invertorAmountMap);
List<Contact> investorList = new List<Contact>();
List<Transaction__c> transactionList = new List<Transaction__c>();
Set<Id> contactIdsSet = new Set<Id>();


investorList = [select id,accountId from contact where Applied_for_SIP__c=true And id in : invertorAmountMap.keyset()];

if(investorList.size()>0)
{
for(Contact con : investorList){
Transaction__c tranObj = new Transaction__c();
tranObj.Investor__c = con.Id;
tranObj.Amount__c= invertorAmountMap.get(con.Id);
tranObj.Transaction_Date__c = date.today();
tranObj.Transaction_Type__c = 'Investment';

contactIdsSet.add(con.Id);
transactionList.add(tranObj);
}

if(transactionList.size()>0)
{
insert transactionList;
TransactionsRollupUpdate btch = new TransactionsRollupUpdate(contactIdsSet);
Database.executeBatch(btch );
}
}
}*/


    public static void caMappingBasedOnFundType(List<Investment__c> invList){

        invList = [SELECT Id, Fund_Type__c, Investor__c, Contribution_Agreement__c, Startup_Round__r.International_Deal__c FROM Investment__c WHERE Id IN :invList];

        //  Auto-selection of Fund (at Investment Level) for Gift City Deals
        //  Added by Sahilparvat on 14.08.2025
        Fund_Module__c internationalFund = [SELECT Id FROM Fund_Module__c WHERE Name = 'International Fund' LIMIT 1];
        System.debug('International Id ::: ' + internationalFund.Id);
        
        System.debug('invList size ::: ' + invList.size());

        for (Investment__c inv : invList) {
            if(inv.Startup_Round__r.International_Deal__c == true) {
                Id internationalFundId = (Id) internationalFund.Id;
                inv.Fund_Type__c = (Id) internationalFundId;
                System.debug('International FUND ID ::: ' + inv.Fund_Type__c);
            }
        }

        Set<Id> investorCAIds = new Set<Id>();
        List<Contribution_Agreement__c> matchedCAList = new List<Contribution_Agreement__c>();
        Map<Id , Id> caFundModuleMap = new Map<Id , Id>();
        Map<Id,Set<Id>> caInvestorIdMap = new Map<Id,Set<Id>>();


        for (Investment__c inv : invList){
            if(inv.Fund_Type__c != null && inv.Investor__c != null)
            {
                investorCAIds.add(inv.Investor__c);
            }
        }

        if(investorCAIds.size() > 0)
        {
            matchedCAList = [SELECT Id , Fund_Onboarded_on__c , Fund_Onboarded_on__r.Name ,  Investor1__c , Investor2__c FROM Contribution_Agreement__c WHERE Investor1__c IN :investorCAIds OR Investor2__c IN :investorCAIds];
        }   

        System.debug('The size of matched CA List Is :::: ' + matchedCAList.size());

        if(matchedCAList.size() > 0)
        {
            for(Contribution_Agreement__c ca : matchedCAList)
            {
                System.debug('CA Fund Type Name ::: ' + ca.Fund_Onboarded_on__r.Name);
                caFundModuleMap.put(ca.Id , ca.Fund_Onboarded_on__c);

                if(!caInvestorIdMap.containsKey(ca.Investor1__c))
                {
                    caInvestorIdMap.put(ca.Investor1__c, new Set<Id>());
                }
                caInvestorIdMap.get(ca.Investor1__c).add(ca.Id);
                
            }
        }

        for (Investment__c inv : invList) {
            if(inv.Fund_Type__c != null && inv.Investor__c != null && caInvestorIdMap.containsKey(inv.Investor__c))
            {
                for(Id caId : caInvestorIdMap.get(inv.Investor__c))
                {
                    if(inv.Fund_Type__c == caFundModuleMap.get(caId))
                    {
                        inv.Contribution_Agreement__c = caId;
                    }
                    // else 
                    // {
                    //     inv.Contribution_Agreement__c = null;
                    // }
                }
            }
        }
    }

    // added by Sahilparvat on 04.08.2025 for PAN Validation
    public static void validateInvestorPAN(List<Investment__c> invList) {

        Set<Id> investorIds = new Set<Id>();

        for(Investment__c inv : invList) {
        
            if(inv.Investor__c != null && String.isNotBlank(inv.Investor_s_PAN__c)) {
                investorIds.add(inv.Investor__c);
            }
        }

        if(investorIds.isEmpty()) {
            return;
        }

        Map<Id, Contact> investorMap = new Map<Id, Contact>([
            SELECT Id, Investor_s_PAN__c
            FROM Contact
            WHERE Id IN :investorIds
        ]);

        for(Investment__c inv : invList) {
        
            if(inv.Investor__c == null || String.isBlank(inv.Investor_s_PAN__c)) {
                continue;
            }

            Contact investor = investorMap.get(inv.Investor__c);

            if(investor == null) {
                continue;
            }

            String investmentPAN = normalizeString(inv.Investor_s_PAN__c);
            String investorPAN = normalizeString(investor.Investor_s_PAN__c);

            if(String.isNotBlank(investorPAN) && !investmentPAN.equals(investorPAN)) {
                inv.addError('PAN number does not match with the investor\'s PAN number. Please enter the correct the PAN number.');
            }
        }
    }

    // added by Sahilparvat on 04.08.2025 for PAN Validation
    public static String normalizeString(String inputString) {
        if(String.isBlank(inputString)) {
            return '';
        }

        // Trim, remove all whitespace, and convert to uppercase for consistent comparison
        return inputString.trim().replaceAll('\\s+', '').toUpperCase();
    }
}