global class StartupRestAPIController {
    
    private static String CLASS_NAME = 'StartupRestAPIController';
    global static string accessToken;
    
    @future(callout=true)
    public static void updateStartupDetails(set<ID> roundId,boolean isInsert){
        system.debug('updateStartupDetails ---'+isInsert+'---'+roundId);
        
        try{
            Map<String,String> credMAp = new Map<String,String>();
            String jsonData;
            String endURLSetting;
            credMAp.put('password','hello');	
            credMAp.put('mobile','8866104284');
            credMAp.put('country_code','+91');
            
            List<API_Setting__c> settingList = API_Setting__c.getall().values();
            system.debug('StartupRestAPIController API_Setting__c>>>'+settingList);
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].End_URL__c))
            {
                endURLSetting = ''+settingList[0].End_URL__c;
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Username__c))
            {
                credMAp.put('mobile',''+settingList[0].Mobile_Username__c);
            }
            if(settingList!=null && settingList.size()>0 && !String.isEmpty(settingList[0].Mobile_Password__c))
            {
                credMAp.put('password',''+settingList[0].Mobile_Password__c);
            }
            
            system.debug('--credMAp--'+credMAp);
            system.debug('--endURLSetting--'+endURLSetting);
            
            
            List<Startup_Round__c> rounds = [select Id,Name, Valuation_Cap__c,Round_type__c,
                                             Lead_Member__r.Name,Co_Lead_Member__c,Co_Lead_Member__r.Name,Lead_Analyst__r.Name,
                                             Date_Of_Founders_Call__c,Currency__c,Doi_Percent_Fee__c,Doi_Percent_Equity__c,
                                             Revenue_Share__c,Face_Value__c,Paid_Up_Value__c,Issue_Price__c,
                                             Type_And_Class_Of_Shares__c,Pre_Money_Valuation__c,Post_Money_Valuation__c,
                                             Total_Investment_P_S__c,Type_Of_Converted_Securities__c,IPV_Shareholding_Percent__c,
                                             Date_Of_Board_Resolution__c,Date_Of_SpecialResolution__c,Date_Of_MGT_14__c,
                                             Date_Of_Opening_Bank_Account__c,Date_Of_Offer_Letter__c,Date_Of_Share_Application_Form__c,
                                             Date_Of_Power_Of_Attorney__c,Date_Of_Signing_Term_sheet__c,
                                             Date_Of_Sssha__c,Date_Of_Allotment_Of_Shares__c,Backed_Out_Investors__c,
                                             Investment_Fee_Startup__c,Number_Of_Share_Holders__c,
                                             Refund_To_Investors__c,Refund_Amount__c,
                                             Poa_And_Board_Seat_Holder__c,Poa_And_Board_Seat_Holder__r.Name,
                                             Poa_And_Observer_Seat__c,Poa_And_Observer_Seat__r.Name,Date_of_sending_out_call_for_money_AIF__c,Date_of_sending_out_call_for_money__c,
                                             Startup__r.Legal_Name__c,
                                             Startup__r.Public_Name__c,
                                             Startup__r.Referred_By__c,
                                             Startup__r.Description__c,
                                             Startup__r.Referral_Date__c,
                                             Startup__r.Investment_Fee_Startup__c,
                                             Startup__r.PAN__c,
                                             Startup__r.Industry__c,
                                             Startup__r.Portfolio_map_Sector__c,
                                             Date_Of_Exit__c, Exit_Price__c,Premier_Deal__c,
                                             Date_of_sending_FnF_Call_for_Money__c,
                                             Supporting_Analyst__r.name,
                                             HQ_Mentor__r.name,
                                             Syndicate_Owner__c,
                                             External_Deal__c,
                                             App_Category__c,
                                             Stage__c,
                                             Pre_Emptive_Deal__c,
                                             Portfolio_Statement_Type__c,
                                             Date_of_Follow_round__c,
                                             Deals_Rating__c,
                                             HQ_Members_Invested__c,
                                             Follow_On_Price__c,
                                             Startup__r.Applicable_for_Secondary_App__c,
                                             Currency_Conversion_Rate__c,
                                             Startup__r.Reserve_Price__c,
                                             Startup__r.Selling_Share_Price_Secondary__c,
                                             Startup__r.Applicable_for_Secondary_Selling__c
                                             FROM Startup_Round__c where id IN :roundId ];
            
            
            if(!rounds.isEmpty()){
                JSONGenerator jsonGen = JSON.createGenerator(true);
                jsonGen.writeStartObject(); 
                jsonGen.writeFieldName('list');
                jsonGen.writeStartArray();
                for(Startup_Round__c startup : rounds ){
                    jsonGen.writeStartObject();
                    if(startup.Startup__r.Legal_Name__c != null) jsonGen.writeStringField('legal_name',startup.Startup__r.Legal_Name__c);
                    if(startup.Startup__c != null) jsonGen.writeStringField('salesforce_startup_id',startup.Startup__c);
                    if(startup.Id != null) jsonGen.writeStringField('salesforce_startup_round_id',startup.Id);
                    
                    if(startup.Name != null)
                    {
                        //jsonGen.writeStringField('public_name',startup.Name);
                        jsonGen.writeStringField('public_name',getPublicName(startup.Name,startup.Startup__r.Public_Name__c));
                    }
                    if(startup.Startup__r.Referral_Date__c != null) jsonGen.writeDateField('referral_date',startup.Startup__r.Referral_Date__c);
                    
                    if(startup.Currency__c != 'USD'){
                        if(startup.Currency__c != null){
                            jsonGen.writeStringField('currency','1');
                        }
                    }else{
                        if(startup.Currency__c != null)
                        {
                            jsonGen.writeStringField('currency','2');
                        }
                    }
                    
                    if(startup.Lead_Member__c != null) jsonGen.writeStringField('lead_member',startup.Lead_Member__r.Name);
                    if(startup.Lead_Analyst__c != null) jsonGen.writeStringField('lead_analyst',startup.Lead_Analyst__r.Name);
                    if(startup.Startup__r.Referred_By__c != null) jsonGen.writeStringField('referred_by',startup.Startup__r.Referred_By__c);
                    if(startup.Doi_Percent_Fee__c != null) jsonGen.writeNumberField('doi_percent_fee',startup.Doi_Percent_Fee__c);
                    if(startup.Doi_Percent_Equity__c != null) jsonGen.writeNumberField('doi_percent_quity',startup.Doi_Percent_Equity__c);
                    if(startup.Date_Of_Founders_Call__c != null) jsonGen.writeDateField('date_of_founders_call',startup.Date_Of_Founders_Call__c);
                    if(startup.Pre_Money_Valuation__c != null) jsonGen.writeNumberField('pre_money_valuation',startup.Pre_Money_Valuation__c);            
                    if(startup.Valuation_Cap__c != null) jsonGen.writeNumberField('valuation_cap',startup.Valuation_Cap__c);
                  //if(startup.Type_Of_Converted_Securities__c != null) jsonGen.writeStringField('type_of_converted_security_id',startup.Type_Of_Converted_Securities__c);
                    if(startup.Startup__r.Industry__c != null){
                        jsonGen.writeStringField('industry',startup.Startup__r.Industry__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNullField('industry');
                    }
                    
                    if(Startup.Startup__r.Portfolio_map_Sector__c != null) jsonGen.writeStringField('industry',Startup.Startup__r.Portfolio_map_Sector__c);
                    if(startup.Startup__r.Description__c != null) {
                        jsonGen.writeStringField('description',startup.Startup__r.Description__c);
                    }else{
                        jsonGen.writeNullField('description');
                    }
                    
                    if(startup.Co_Lead_Member__c != null) jsonGen.writeStringField('co_lead_member',startup.Co_Lead_Member__r.Name);
                    if(startup.Revenue_Share__c != null) jsonGen.writeNumberField('revenue_share',startup.Revenue_Share__c);
                    if(startup.Face_Value__c != null){
                        jsonGen.writeNumberField('face_value',startup.Face_Value__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNumberField('face_value',0);
                    }
                    
                    if(startup.Paid_Up_Value__c != null){
                        jsonGen.writeNumberField('paid_up_value',startup.Paid_Up_Value__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNumberField('paid_up_value',0);
                    }
                    if(startup.Issue_Price__c != null){
                        jsonGen.writeNumberField('issue_price',startup.Issue_Price__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNullField('issue_price');
                    }
                    
                    if(startup.type_and_class_of_shares__c != null)
                        jsonGen.writeStringField('type_and_class_of_shares',startup.type_and_class_of_shares__c);
                    else 
                        jsonGen.writeNullField('type_and_class_of_shares');
                    if(startup.Post_Money_Valuation__c != null) jsonGen.writeNumberField('post_valuation',startup.Post_Money_Valuation__c);
                    if(startup.Total_Investment_P_S__c != null) jsonGen.writeNumberField('total_investment',startup.Total_Investment_P_S__c);
                    if(startup.IPV_Shareholding_Percent__c != null) jsonGen.writeNumberField('ipv_shareholding_percent',startup.IPV_Shareholding_Percent__c);
                    if(startup.Date_Of_Board_Resolution__c != null) jsonGen.writeDateField('date_of_board_resolution',startup.Date_Of_Board_Resolution__c);
                    if(startup.Date_Of_SpecialResolution__c != null) jsonGen.writeDateField('date_of_special_resolution',startup.Date_Of_SpecialResolution__c);
                    if(startup.Date_Of_MGT_14__c != null) jsonGen.writeDateField('date_of_mgt_14',startup.Date_Of_MGT_14__c);
                    if(startup.Date_Of_Opening_Bank_Account__c != null) jsonGen.writeDateField('date_of_opening_bank_account',startup.Date_Of_Opening_Bank_Account__c);
                    if(startup.Date_Of_Offer_Letter__c != null) jsonGen.writeDateField('date_of_offer_letter',startup.Date_Of_Offer_Letter__c);
                    if(startup.Date_Of_Share_Application_Form__c != null) jsonGen.writeDateField('date_of_share_application_form',startup.Date_Of_Share_Application_Form__c);
                    if(startup.Date_Of_Power_Of_Attorney__c != null) jsonGen.writeDateField('date_of_power_of_attorney',startup.Date_Of_Power_Of_Attorney__c);
                    if(startup.Date_Of_Signing_Term_sheet__c != null) jsonGen.writeDateField('date_of_signing_termsheet',startup.Date_Of_Signing_Term_sheet__c);
                    if(startup.Date_Of_Sssha__c != null) jsonGen.writeDateField('date_of_sssha',startup.Date_Of_Sssha__c);
                    if(startup.Date_Of_Allotment_Of_Shares__c != null){
                        jsonGen.writeDateField('date_of_allotment_of_shares',startup.Date_Of_Allotment_Of_Shares__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNullField('date_of_allotment_of_shares');
                    }
                    
                    if(startup.Poa_And_Board_Seat_Holder__c != null) jsonGen.writeStringField('poa_and_board_seat_holder',startup.Poa_And_Board_Seat_Holder__r.Name);
                    if(startup.Poa_And_Observer_Seat__c != null) jsonGen.writeStringField('poa_and_observer_seat',startup.Poa_And_Observer_Seat__r.Name);
                    if(startup.Investment_Fee_Startup__c != null) jsonGen.writeNumberField('investment_fee_startup',startup.Investment_Fee_Startup__c);
                    if(startup.Number_Of_Share_Holders__c != null) jsonGen.writeStringField('number_of_shareholders',startup.Number_Of_Share_Holders__c);
                    if(startup.Backed_Out_Investors__c != null) jsonGen.writeNumberField('backed_out_investors',startup.Backed_Out_Investors__c);
                    if(startup.Startup__r.PAN__c != null){
                        jsonGen.writeStringField('pan',startup.Startup__r.PAN__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNullField('pan');
                    }
                    
                    if(startup.Refund_To_Investors__c != null) jsonGen.writeStringField('refund_to_investors',startup.Refund_To_Investors__c);
                    if(startup.Refund_Amount__c != null) jsonGen.writeNumberField('refund_amount',startup.Refund_Amount__c);
                    //Akash: 8/7/22: Adding exit details for sync
                    if(startup.Round_type__c != null) jsonGen.writeStringField('startup_round_type' , startup.Round_type__c);
                    if(startup.Exit_Price__c != null){
                        jsonGen.writeNumberField('exit_price',startup.Exit_Price__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNullField('exit_price');
                    }
                    
                    if(startup.Date_Of_Exit__c != null){
                        jsonGen.writeDateField('exit_date',startup.Date_Of_Exit__c);
                    }else{
                        //added by bharat for null handling for app syncing on 21-11-2024
                        jsonGen.writeNullField('exit_date');
                    }
                    
                    if(startup.HQ_Mentor__r.Name != null) jsonGen.writeStringField('lead_mentor' , startup.HQ_Mentor__r.Name);
                    if(Startup.Premier_Deal__c) jsonGen.writeBooleanField('Premier_Deal',Startup.Premier_Deal__c);
                    //Added by ankush 20.2.23 for round sync on app 
                    //Added date fields PLT,FnF and AIF by ankush for app Pending docs.
                    if(Startup.name != null) jsonGen.writeStringField('Round_name', Startup.name);
                    If(Startup.Date_of_sending_FnF_Call_for_Money__c !=null) jsonGen.writeDateField('Date_of_sending_FnF_Call_for_Money', Startup.Date_of_sending_FnF_Call_for_Money__c);
                    If(Startup.Date_of_sending_out_call_for_money__c !=null) jsonGen.writeDateField('Date_of_sending_out_call_for_money_PLT', Startup.Date_of_sending_out_call_for_money__c);
                    If(Startup.Date_of_sending_out_call_for_money_AIF__c !=null) jsonGen.writeDateField('Date_of_sending_out_call_for_money_AIF', Startup.Date_of_sending_out_call_for_money_AIF__c);
                    if(startup.Type_Of_Converted_Securities__c != null && Startup.Type_Of_Converted_Securities__c == 'Preferred stock') jsonGen.writeNumberField('type_of_converted_security_id', 1);
                    if(startup.Type_Of_Converted_Securities__c != null && Startup.Type_Of_Converted_Securities__c == 'Debt') jsonGen.writeNumberField('type_of_converted_security_id', 3);
					if(startup.Type_Of_Converted_Securities__c != null && Startup.Type_Of_Converted_Securities__c == 'CCPS') jsonGen.writeNumberField('type_of_converted_security_id', 7);
                    if(startup.Type_Of_Converted_Securities__c != null && Startup.Type_Of_Converted_Securities__c == 'Pre series A') jsonGen.writeNumberField('type_of_converted_security_id', 6);
                    if(startup.Type_Of_Converted_Securities__c != null && Startup.Type_Of_Converted_Securities__c == 'Seed Series CCPS') jsonGen.writeNumberField('type_of_converted_security_id', 5);
                    if(startup.Type_Of_Converted_Securities__c != null && Startup.Type_Of_Converted_Securities__c == 'CCD') jsonGen.writeNumberField('type_of_converted_security_id', 8);
					if(Startup.Supporting_Analyst__c != null) jsonGen.writeStringField('lead_support_analyts',Startup.Supporting_Analyst__r.name);
                   	if(Startup.Syndicate_Owner__c != null) jsonGen.writeStringField('syndicate_owner_account_id',Startup.Syndicate_Owner__c);
                   	if(Startup.External_Deal__c != null) jsonGen.writeStringField('external_deal',Startup.External_Deal__c);
                    //jsonGen.writeStringField('investment_type','other'); 
                    jsonGen.writeNumberField('investment_type_id',5); 
                    
                    //Added By Bharat FOR Restructuring of Startup Stages Requirement
                    if(Startup.Stage__c != null){
                        jsonGen.writeStringField('app_stage',Startup.Stage__c);
                    }else{
                        jsonGen.writeNullField('app_stage');
                    }
                    
                    if(Startup.App_Category__c == 'Open For Commitment'){
                        jsonGen.writeNumberField('app_category',5);
                    }else if(Startup.App_Category__c == 'Upcoming Deals'){
                        jsonGen.writeNumberField('app_category',6);
                    }else if(Startup.App_Category__c == 'Evaluation In progress'){
                        jsonGen.writeNumberField('app_category',7);
                    }else if(Startup.App_Category__c == 'Commitment Closed'){
                        jsonGen.writeNumberField('app_category',8);
                    }else if(Startup.App_Category__c == 'Funding Closed'){
                        jsonGen.writeNumberField('app_category',9);
                    }else if(Startup.App_Category__c == 'Deals Dropped'){
                        jsonGen.writeNumberField('app_category',10);
                    }else if(Startup.App_Category__c == null){
                        jsonGen.writeNullField('app_category');
                    }
                    
                    jsonGen.writeBooleanField('is_pre_emptive' , Startup.Pre_Emptive_Deal__c);
                    
                    //Added By Bharat For Portfolio Statement Requirement on 04-03-2025
                    if(Startup.Portfolio_Statement_Type__c != null){
                        jsonGen.writeStringFIeld('portfolio_statement_type' , Startup.Portfolio_Statement_Type__c);
                    }else{
                        jsonGen.writeNullField('portfolio_statement_type');
                    }
                    
                    if(Startup.Date_of_Follow_round__c !=  null){
                        jsonGen.WriteDateField('date_of_follow_round' , Startup.Date_of_Follow_round__c);
                    }else{
                        jsonGen.writeNullField('date_of_follow_round');
                    }
                    
                     //Added by jay dabhi on 08.05.2025
                    if(Startup.Deals_Rating__c != null){
                        if(Startup.Deals_Rating__c == '3 Star'){
                            jsonGen.writeNumberField('deals_rating' , 3);
                        }
                        else if(Startup.Deals_Rating__c == '5 Star'){
                            jsonGen.writeNumberField('deals_rating' , 5);
                        }
                        else if(Startup.Deals_Rating__c == '7 Star'){
                            jsonGen.writeNumberField('deals_rating' , 7);
                        }
                    }
                    else {
                        jsonGen.writeNullField('deals_rating');
                    }
                    
                    if(Startup.HQ_Members_Invested__c !=  null ){
                         jsonGen.writeStringField('hq_members_invested' , Startup.HQ_Members_Invested__c);
                        
                    }
                    
                    //Added by jay dabhi on discuused with madhuri
                    if(Startup.Follow_On_Price__c !=  null ){
                    	jsonGen.writeNumberField('follow_on_price' , Startup.Follow_On_Price__c);
                    }
                    else{
                        jsonGen.writeNullField('follow_on_price');
                    }
                    
                    //Added by Bharat for Portfolion Statement requirement on 29-04-2025
                    if(Startup.Currency_Conversion_Rate__c != null){
                        jsonGen.writeNumberField('currency_conversion_rate' , Startup.Currency_Conversion_Rate__c);
                    }else{
                        jsonGen.writeNullField('currency_conversion_rate');
                    }
                    
                    //Added by Bharat for Secondary Module on 29-04-2025
                    if(Startup.Startup__r.Applicable_for_Secondary_App__c != null){
                        jsonGen.writeStringField('applicable_for_secondary_app',Startup.Startup__r.Applicable_for_Secondary_App__c);
                    }else{
                        jsonGen.writeNullField('applicable_for_secondary_app');
                    }
                    
                    if(Startup.Startup__r.Reserve_Price__c != null){
                        jsonGen.writeNumberField('reserve_price' , Startup.Startup__r.Reserve_Price__c);
                    }else{
                        jsonGen.writeNullField('reserve_price');
                    }
                    
                    if(Startup.Startup__r.Selling_Share_Price_Secondary__c != null){
                        jsonGen.WriteNumberField('maximum_selling_price' , Startup.Startup__r.Selling_Share_Price_Secondary__c);
                    }/*else{
                        jsonGen.writeNullField('maximum_selling_price');
                    }*/
                    
                    if(Startup.Startup__r.Applicable_for_Secondary_Selling__c){
                        jsonGen.writeBooleanField('applicable_for_secondary_selling' , TRUE);
                    }else{
                        jsonGen.writeBooleanField('applicable_for_secondary_selling', FALSE);
                    }
                    jsonGen.writeEndObject();
                }
                jsonGen.writeEndArray();
                jsonGen.writeEndObject();
                jsonData = jsonGen.getAsString();
                System.debug('Json Data - ' + jsonData);
            }
            HttpRequest req = new HttpRequest();
            String enddURL = endURLSetting+'/userAuthentication/login-admin';
            
            String bodyy =  JSON.serialize(credMAp);
            system.debug('---'+bodyy);
            req.setEndpoint(enddURL);
            req.setHeader('Content-Type','application/json');
            req.setMethod('POST');
            req.setTimeout(120000);
            req.setBody(bodyy);
            Http http = new Http();
            HTTPResponse res = http.send(req);
            system.debug('****');
            System.debug(res.getStatusCode());
            System.debug('res---'+res.toString());
            System.debug('STATUS:'+res.getStatus());
            
            if(res.getStatusCode() == 200){
                system.debug('Data sent'+res.getBody());
                String jsonstr = JSON.serialize(res.getBody());
                JSONParser parser = JSON.createParser(jsonStr);
                Map<String, Object> results = (Map<String, Object>) JSON.deserializeUntyped(res.getBody());
                String tkn = JSON.serialize(results.get('data'));
                Map<String, Object> token = (Map<String, Object>) JSON.deserializeUntyped(tkn);
                accessToken = String.valueOF(token.get('token'));
                system.debug('accessToken  '+token.get('token'));
                system.debug('accessToken in add  '+accessToken);
                //HttpRequest req = new HttpRequest();
                
                if(!isInsert)
                    enddURL = endURLSetting+'/startupBulkUpdate';
                else
                    enddURL = endURLSetting+'/startupBulkAdd';

                HttpRequest request = new HttpRequest();
                request.setEndpoint(enddURL);
                request.setHeader('Authorization','Bearer ' + accessToken);
                request.setHeader('Content-Type','application/json');
                
                if(!isInsert)
                    request.setMethod('PUT');
                else
                    request.setMethod('POST');

                request.setTimeout(120000);
                request.setBody(jsonData);
                Http http1 = new Http();
                HTTPResponse res1 = http1.send(request);
                system.debug('****');
                System.debug(res1.getStatusCode());
                System.debug('res--add-'+res1.getBody());
                System.debug('STATUS:'+res1.getStatus());
                
                
                
            }
        }
        catch(Exception ex){
            system.debug('Error '+ex);
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'updateStartupDetails';
            log.Error_Message__c = ex.getMessage();
            insert log;
        }
    }
    
    public static String getPublicName(String roundName,String strPublicName)
    {        
        String srName = roundName;
        Integer i=0;
        system.debug('Name>>>>'+srName);
        if(srName.startsWith('SR') && srName.contains('-'))
        {
            i = integer.valueof(srName.substringAfterLast('-'));
        }
        else if(srName.contains('_R'))
        {
            i= integer.valueof(srName.substringAfterLast('_R'));
        }
        
        srName = strPublicName;
        if(i>1)
            srName = srName + '_R'+i;
       
        system.debug('Name>>>>'+srName);

        return srName;
    }
    
}