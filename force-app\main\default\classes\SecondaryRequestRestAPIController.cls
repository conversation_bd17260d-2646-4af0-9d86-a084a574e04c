@RestResource(urlMapping='/SecondaryRequestRestAPIController')
global with sharing class SecondaryRequestRestAPIController {
    
    @HttpPost
    global static ResponseWrapper sendRequestDetails() {
        RestRequest req = RestContext.request;
        RestResponse res = RestContext.response;
        
        RequestWrapper reqBody;
        ResponseWrapper errorResponse = new ResponseWrapper();
        Integer PageNo;
        Integer PageSize;
        String baseQuery;
        string req_type;
        
        try {
            
            reqBody = (RequestWrapper) JSON.deserialize(req.requestBody.toString(), RequestWrapper.class);
            System.debug('reqBody>>>>>>>>>>>>>>>>>' + reqBody);
            
            if(reqBody.pageNumber != null && reqBody.perPage != null){
            	PageSize = reqBody.perPage;
            	PageNo = reqBody.pageNumber;
            }
            if (reqBody == null) {
                errorResponse.errorCode = 400;
                errorResponse.message = 'Request Body cannot be blank';
                return errorResponse;
            }else if(String.isBlank(reqBody.salesforce_account_id)){
                errorResponse.errorCode = 400;
                errorResponse.message = 'Account Salesforce ID is required fields';
                return errorResponse;
            }
            ID userAccountId = reqBody.salesforce_account_id;
            if(reqBody.type == 1){
                req_type = 'Buying Intent';
            }else if(reqBody.type == 2){
                req_type = 'Selling Intent';
            }
            
            List<requestDataWrapper> resultList = new List<requestDataWrapper>();
            
            baseQuery = 'Select Id ,Name , Commitment_Request_Amount__c , Reserve_Price__c , Issue_Price_of_Last_Rounds__c , ' +
            				'Date_Time_of_Filling_the_Form__c , Reason_for_Rejection__c , Status__c , Startup__c , ' + 
            				'Startup__r.Public_Name__c , Startup_Round__r.Syndicate_Owner__c , Startup_Round__r.External_Deal__c , ' +
            				'Startup_Round__r.Premier_Deal__c , Bid_Price__c , Startup_Round__c , App_Secondary_Iinvestment_ID__c , Type_of_Intent__c ,' +
                			'Price_per_share_wish_to_sell_at__c , Investment__c,Number_of_shares_wish_to_sell__c , Startup_Round__r.Currency__c FROM Secondary_Module__c WHERE Member_Name__c =: userAccountId AND Type_of_Intent__c =: req_type';
            
            if (reqBody.status_type != null && !reqBody.status_type.isEmpty())
            {
                List<String> statusList = getRequestStatusType(reqBody.status_type);
                baseQuery += ' AND Status__c IN : StatusList';
            }
            
            baseQuery += ' ORDER BY CreatedDate DESC';
            
            if(PageNo != null && PageSize != null){
            	Integer offsetVal = (PageNo - 1) * PageSize;
            	baseQuery +=  ' LIMIT : PageSize OFFSET : offsetVal';
            }
            List<Secondary_Module__c> requestDataList = Database.query(baseQuery);
            
            System.debug('requestDataList>>>>>>>>>>>>>>>>..'+requestDataList);
            System.debug('baseQuery>>>>>>>>>>>>>>>>..'+baseQuery);
            
            if(requestDataList != null && requestDataList.size()>0 ){
                
                for (Secondary_Module__c sm : requestDataList) {
                    
                    StartupDataWrapper sdw = new StartupDataWrapper();
                    sdw.startup_id = sm.Startup__c;
                    sdw.public_name = sm.Startup__r.Public_Name__c ;
                    sdw.syndicate_owner_account_id = sm.Startup_Round__r.Syndicate_Owner__c;
                    sdw.salesforce_startup_round_id = sm.Startup_Round__c;
                    if(sm.Startup_Round__r.External_Deal__c == 'Yes'){
                        sdw.external_deal =  TRUE ;
                    }else if(sm.Startup_Round__r.External_Deal__c == 'No'){
                        sdw.external_deal =  FALSE ;
                    }
                    sdw.isPremierStartup = sm.Startup_Round__r.Premier_Deal__c;
                    
                    statusTypeDataWrapper stdw = new statusTypeDataWrapper();
                    if(sm.Status__c == 'Request Submitted'){
                        stdw.type_id = 0;
                        stdw.type_text = 'Request Submitted';
                        stdw.color_hex = '#CEF5CF';
                        stdw.text_color_hex = '#017F04';
                    }else if(sm.Status__c == 'Transaction Done'){
                        stdw.type_id = 1;
                        stdw.type_text = 'Transaction Done';
                        stdw.color_hex = '#CBECFF';
                        stdw.text_color_hex = '#0075BB';
                    }else if(sm.Status__c == 'Under Review'){
                        stdw.type_id = 2;
                        stdw.type_text = 'Under Review';
                        stdw.color_hex = '#FFF3C3';
                        stdw.text_color_hex = '#EBBC00';
                    }else if(sm.Status__c == 'Rejected'){
                        stdw.type_id = 3;
                        stdw.type_text = 'Rejected';
                        stdw.color_hex = '#FFE6E6';
                        stdw.text_color_hex = '#CF0401';
                    }else if(sm.Status__c == 'In Process'){
                        stdw.type_id = 4;
                        stdw.type_text = 'In Process';
                        stdw.color_hex = '#FFA8EB';
                        stdw.text_color_hex = '#980075';
                    }
                    requestDataWrapper rdw = new requestDataWrapper();
                    rdw.startup = sdw;
                    if(sm.Commitment_Request_Amount__c != null){
                        rdw.invested_amount = sm.Commitment_Request_Amount__c.round();
                    }else{
                        rdw.invested_amount = 0;
                    }
                    if(sm.Reserve_Price__c != null){
                        rdw.reserve_price = sm.Reserve_Price__c.round();
                    }else{
                        rdw.reserve_price = 0;
                    }
                    if(sm.Bid_Price__c!= null){
                        rdw.share_price = sm.Bid_Price__c.round();
                    }else{
                        rdw.share_price = 0;
                    }
                        
                    
                    System.debug('invested_amount>>>>>>>' + rdw.invested_amount + '    reserve_price>>>>>>>>>>>>>' + rdw.reserve_price + '       share_price>>>>>>>>>' + rdw.share_price);
                    rdw.created_date = Date.valueOf(sm.Date_Time_of_Filling_the_Form__c);
                    rdw.rejected_reason = sm.Reason_for_Rejection__c;
                    rdw.status_type = stdw;
                    rdw.app_secondary_investment_id = Integer.valueOf(sm.App_Secondary_Iinvestment_ID__c);
                    if(sm.Type_of_Intent__c == 'Buying Intent'){
                        rdw.type_of_intent = 1;
                    }else if (sm.Type_of_Intent__c == 'Selling Intent'){
                        rdw.type_of_intent = 2;
                    }
                    rdw.selling_share_price = Integer.valueOf(sm.Price_per_share_wish_to_sell_at__c);
                    rdw.salesforce_investment_id = sm.Investment__c;
                    rdw.no_of_shares_wish_to_sell = sm.Number_of_shares_wish_to_sell__c;
                    if(sm.Startup_Round__r.Currency__c == 'INR'|| sm.Startup_Round__r.Currency__c == null ){
                        rdw.currency_id = 1;
                    }else if(sm.Startup_Round__r.Currency__c == 'USD'){
                        rdw.currency_id = 2;
                    }
                    resultList.add(rdw);
                }
            }
            
            System.debug('resultList>>>>>>>>>.' + resultList.Size());
            System.debug('resultList>>>>>>>>>.' + resultList);
            ResponseWrapper response = new ResponseWrapper();
            response.message = 'Success';
            response.data = resultList;
            response.errorCode = 200;
            response.error = false;
            
            //res.statusCode = 200;
            //res.responseBody = Blob.valueOf(JSON.serialize(response));
            
            return response;
        
        } catch (Exception ex) {
            errorResponse.errorCode = 400;
            errorResponse.message = ex.getMessage();
            return errorResponse;
        }
    }

    public static List<String> getRequestStatusType(List<Integer> status_type) {
        List<String> statusList = New List<String>();
        for(Integer num : status_type){
            if(num == 0){
                statusList.add('Request Submitted');
            }
            if(num == 1){
                statusList.add('Transaction Done');
            }
            if(num == 2){
                statusList.add('Under Review');
            }
            if(num == 3){
                statusList.add('Rejected');
            }
            if(num == 4){
                statusList.add('In Process');
            }
        }
        return statusList;
    }

     global class RequestWrapper {
        public Integer pageNumber;
        public Integer perPage;
        public List<Integer> status_type;
        public String salesforce_account_id;
        public Integer type;
    }

    global class StartupDataWrapper {
        public String startup_id;
        public String public_name;
        public String syndicate_owner_account_id;
        public Boolean external_deal;
        public Boolean isPremierStartup;
        public String salesforce_startup_round_id;
    }

    global class requestDataWrapper {
        public StartupDataWrapper startup;
        public Decimal invested_amount;
        public Decimal reserve_price;
        public Decimal share_price;
        public Date created_date;
        public String rejected_reason;
        public statusTypeDataWrapper status_type;
        Public Decimal app_secondary_investment_id;
        public integer type_of_intent;
        public Integer selling_share_price;
        public String salesforce_investment_id; 
        public Decimal no_of_shares_wish_to_sell;
        public integer currency_id;
    }

    global class statusTypeDataWrapper{
        public Integer type_id;
        public String type_text;
        public String color_hex;
        public String text_color_hex;
    }
    global class ResponseWrapper {
        public String message;
        public List<requestDataWrapper> data;
        public Integer errorCode;
        public Boolean error;
    }
}