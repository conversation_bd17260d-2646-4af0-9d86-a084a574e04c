public with sharing class ReferralTaskTableController {
    @AuraEnabled(cacheable=true)
    public static List<Task>ReferralTaskTable(Id accountId) {
        
        
        List<Task> listName =[select id,Referral_Trigger_Type__c,CreatedDate,Referral_Trigger_Status__c,Call_Date__c,Outcome__c 
                              from Task where Referral_Trigger_Status__c != null  AND WhatId = :accountId 
          AND What.Type = 'Account' 
                                     ORDER By CreatedDate DESC
                             ];
        
        return listName;
        
    }
    @AuraEnabled(cacheable=true)
    public static Map<Id, Integer> ReferralWithTrigger(Id recordId) {
        Map<Id, Integer> referralMap = new Map<Id, Integer>();
        
     
        List<Task> tasks = [
            SELECT Id, CreatedDate, WhatId
            FROM Task
            WHERE Referral_Trigger_Status__c != null
            AND WhatId = :recordId
            ORDER BY CreatedDate DESC
        ];
        
        if (tasks.isEmpty()) {
            return referralMap;
        }
        
     
        Date latestTriggerDate = tasks[0].CreatedDate.date();
        Date referralEndDate = latestTriggerDate.addDays(30);
        
      
        List<AggregateResult> referralResults = [
            SELECT COUNT(Id) IdCount, Referred_By__c
            FROM Lead__c
            WHERE Date_of_receiving_lead__c >= :latestTriggerDate
            AND Date_of_receiving_lead__c <= :referralEndDate
            AND Referred_By__c != null
            GROUP BY Referred_By__c
        ];
        
        for (AggregateResult result : referralResults) {
            Id referredBy = (Id) result.get('Referred_By__c');
            Integer count = (Integer) result.get('IdCount');
            referralMap.put(referredBy, count);
        }
        
        return referralMap;
    }
    @AuraEnabled(cacheable=true)
public static Map<Id, Integer> ReferralWithoutTrigger(Id recordId) {
    Map<Id, Integer> referralMap = new Map<Id, Integer>();

   
    List<Task> triggerTasks = [
        SELECT Id, CreatedDate
        FROM Task
        WHERE Referral_Trigger_Status__c != null
        AND WhatId = :recordId
        ORDER BY CreatedDate ASC
    ];

  
    List<Date> startDates = new List<Date>();
    List<Date> endDates = new List<Date>();

    for (Task t : triggerTasks) {
        Date start = t.CreatedDate.date();
        Date endD = start.addDays(30);
        startDates.add(start);
        endDates.add(endD);
    }

    
    List<Lead__c> leads = [
        SELECT Id, Referred_By__c, Date_of_receiving_lead__c
        FROM Lead__c
        WHERE Referred_By__c = :recordId
    ];

    for (Lead__c lead : leads) {
        Date leadDate = lead.Date_of_receiving_lead__c;

        Boolean isInTriggerWindow = false;

       
        for (Integer i = 0; i < startDates.size(); i++) {
            if (leadDate >= startDates[i] && leadDate <= endDates[i]) {
                isInTriggerWindow = true;
                break;
            }
        }

       
        if (!isInTriggerWindow) {
            Id referredBy = lead.Referred_By__c;
            Integer current = referralMap.containsKey(referredBy) ? referralMap.get(referredBy) : 0;
            referralMap.put(referredBy, current + 1);
        }
    }

    return referralMap;
}

    
}