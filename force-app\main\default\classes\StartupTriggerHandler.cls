public class StartupTriggerHandler
{
    public void beforeInsert(List<Startup__c> startupList)
    {
        Integer lastNo = getLastSequenceNo();        
        system.debug('lastNo >>'+lastNo );

        for(Startup__c st : startupList)
        {
            system.debug('st.Startup_Unique_Number__c>>>'+st.Startup_Unique_Number__c);
            //if(String.IsBlank(''+st.Startup_Unique_Number__c) || st.Startup_Unique_Number__c==null){
                lastNo = lastNo + 1;
                system.debug('new no>>>>'+lastNo);
                system.debug('st Public_Name__c>>>>'+st.Public_Name__c);
                
                if(st.Public_Name__c!=null && st.Public_Name__c!='')
                {
                    String publicStr = st.Public_Name__c;

                    if(publicStr.length()>5)
                    {
                        publicStr = publicStr.substring(0, 5);
                    }
                    publicStr = publicStr.remove('-');
                    st.Startup_Unique_Number__c = publicStr +'-'+lastNo;
                }
                else
                    st.Startup_Unique_Number__c = 'S-'+lastNo;
            //}
        }
    }
    
    public void afterInsert(List<Startup__c> startupList){
        createPointTransaction(startupList);
    }

    
    public void afterUpdate(List<Startup__c> startupList,Map<Id,Startup__c> triggerOldMap)
    {
        List<API_Setting__c> settingList = API_Setting__c.getall().values();
        system.debug('Contact trigger API_Setting__c>>>'+settingList);
        if(settingList!=null && settingList.size()>0 && settingList[0].Enable_Startup_API_Call__c)
        {
            Set<Id> roundForAPI1 = new Set<Id>();
            Set<Id> roundForAPI2 = new Set<Id>();
            Set<Id> startupIds = new Set<Id>();
            for(Startup__c str : startupList)
            {
                if(
                //str.Industry__c != triggerOldMap.get(str.Id).Industry__c
                Str.Portfolio_map_Sector__c != triggerOldMap.get(str.id).Portfolio_map_Sector__c
                || str.PAN__c != triggerOldMap.get(str.Id).PAN__c
                || str.Public_Name__c != triggerOldMap.get(str.Id).Public_Name__c
                || str.Legal_Name__c != triggerOldMap.get(str.Id).Legal_Name__c
                || str.Description__c != triggerOldMap.get(str.Id).Description__c
                || str.Applicable_for_Secondary_App__c != triggerOldMap.get(str.Id).Applicable_for_Secondary_App__c
               	|| str.Selling_Share_Price_Secondary__c != triggerOldMap.get(str.Id).Selling_Share_Price_Secondary__c
                || str.Total_amount_Rec_Buying_Intent__c != triggerOldMap.get(str.Id).Total_amount_Rec_Buying_Intent__c)
                {
                    startupIds.add(str.Id);
                }
            }
            system.debug('startupIds>>>>'+startupIds);
            if(!startupIds.isEmpty())
            {
                Integer count = 0;

                for(Startup_Round__c sRound : [Select id from Startup_Round__c where Startup__c in : startupIds])
                {
                    if(count < 100)
                        roundForAPI1.add(sRound.ID);
                    else
                        roundForAPI2.add(sRound.ID);
                    
                    count ++;
                }
                
                system.debug('startup TRIGGER roundForAPI1 >>>>'+roundForAPI1);
                system.debug('startup TRIGGER roundForAPI2 >>>>'+roundForAPI2 );
                if(roundForAPI1.size()>0){
                    StartupRestAPIController.updateStartupDetails(roundForAPI1,false);
                }
                if(roundForAPI2.size()>0){
                    StartupRestAPIController.updateStartupDetails(roundForAPI2,false);
                }
            }
        }
    }
    
    public Integer getLastSequenceNo(){
        List<Startup__c> existingStartupList = new List<Startup__c>();
        String lastNo;
        Integer lastNoInt = 0;
        
        existingStartupList = [select id,name,Startup_Unique_Number__c from Startup__c where Startup_Unique_Number__c!=null order by name Desc limit 1];
        
        if(existingStartupList!=null && existingStartupList.size()>0 && existingStartupList[0].Startup_Unique_Number__c!=null)
        {
            lastNo = existingStartupList[0].Startup_Unique_Number__c;
            
            Integer index = lastNo.lastindexOf('-')+1;
            system.debug('index>>'+index);
            String lastNoStr = lastNo.substring(index);
            system.debug('hjhj>>'+lastNoStr );
            lastNoInt = Integer.valueOf(lastNoStr);
        }
        
        return lastNoInt;
    }
    
    public void createPointTransaction(List<Startup__c> startupList){
        List<Points_Transaction__c> points = new List<Points_Transaction__c>();
        for(Startup__c str : startupList){ 
            system.debug(':::'+str.Referred_By_Internal__c);
            if(str.Referred_By_Internal__c != null){
                Points_Transaction__c point =  new Points_Transaction__c();
                point.Credit_To__c = str.Referred_By_Internal__c;
                point.Debit_From__c = Label.PT_Debit_Account;               
                point.Points_Alloted__c = 20;
                point.Date_of_Receiving_Lead__c = system.today();
                point.Point_Type__c = 'Refer a Startup';
                point.Payment_Date__c = system.today();
                point.date__c = system.today(); 
                point.Startup__c = str.Id;
                point.Refer_Date__c = system.today();
                point.Member_Name__c = str.Referred_By_Internal__c;
                points.add(point);
            }                
        }        
        
		if(!points.isEmpty()){
            insert points; 
        }        
    }
}