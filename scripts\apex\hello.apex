// Use .apex files to store anonymous Apex.
// You can execute anonymous Apex in VS Code by selecting the
//     apex text and running the command:
//     SFDX: Execute Anonymous Apex with Currently Selected Text
// You can also execute the entire file by running the command:
//     SFDX: Execute Anonymous Apex with Editor Contents

// System.debug('Hello World!');
// DealTrackerTableOnStartupRound.dealTrackerTableDetails('a0G0l000007XphbEAC');

Map<String , Object> dummy = DealTrackerTableOnStartupRound.dealTrackerTableDetails('a0G0l000007XphbEAC');

