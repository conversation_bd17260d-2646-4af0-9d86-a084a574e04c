public class DealTrackerTableOnStartupRound {
    
    @AuraEnabled(cacheable=true)
    public static Map<String, Map<String, Object>> dealTrackerTableDetails(Id startupRoundId){
        
        List<Startup_Round__c> startupRoundList = [SELECT Id , Name , AIF_No_of_Investors__c , AIF_No_of_Backed_out_Investors__c , AIF_Actual_Commitment_Amount__c , AIF_Expected_Amount__c , AIF_CFM_Sent_Amount__c , AIF_CFM_Yet_to_Go_Amount__c , AIF_Amount_Received_from_Investors__c , AIF_Amount_Pending_from_Investors__c , AIF_Amount_Transferred_to_Startup__c , FnF_No_of_Investors__c , AIF_Amount_Pending_to_Startup__c , AIF_Waitlist_Amount__c , AIF_Buffer_Amount__c , AIF_MGT_14_Amount__c , AIF_Backed_out_Amounts__c  FROM Startup_Round__c WHERE Id = :startupRoundId LIMIT 1];
        
        List<Investment__c> investmentList = [SELECT Id , Name , Investor__c , Investor__r.Name FROM Investment__c WHERE Startup_Round__c IN :startupRoundList];
        
        System.debug('Startup Round ::: ' + startupRoundList);
        System.debug('Investments are ::: ' + investmentList.size());
        System.debug(' ');

        Map<String , Map<String , Object> > dealTrackerTableMapping = new Map<String , Map<String , Object> >();
        
        
        {
        
        // 1. No. of Investors 
        List<AggregateResult> noOfInvestor_FundA = [SELECT COUNT(Id) noOfInvestor FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> noOfInvestor_FundB = [SELECT COUNT(Id) noOfInvestor FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> noOfInvestor_FundInt = [SELECT COUNT(Id) noOfInvestor FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal noOfInvestorFundA = (Decimal) noOfInvestor_FundA[0].get('noOfInvestor') != null ? (Decimal) noOfInvestor_FundA[0].get('noOfInvestor') : 0;
        Decimal noOfInvestorFundB = (Decimal) noOfInvestor_FundB[0].get('noOfInvestor') != null ? (Decimal) noOfInvestor_FundB[0].get('noOfInvestor') : 0;
        Decimal noOfInvestorFundInt = (Decimal) noOfInvestor_FundInt[0].get('noOfInvestor') != null ? (Decimal) noOfInvestor_FundInt[0].get('noOfInvestor') : 0;
        Decimal startupRoundAifNoOfInvestors = startupRoundList[0].AIF_No_of_Investors__c != null ? startupRoundList[0].AIF_No_of_Investors__c : 0;

        dealTrackerTableMapping.put('No. of Investors (Excluding IPV)' , new Map<String , Object>{
                'Direct' => startupRoundAifNoOfInvestors , 'Fund A' => noOfInvestorFundA , 'Fund B' => noOfInvestorFundB , 'International Fund' => noOfInvestorFundInt
        });

        
        
        //  --------------------------------------------------------------------
        
        // 2. No of Shares 
        
        dealTrackerTableMapping.put('No of Shares' , new Map<String , Object>{
            'Direct' => 'Coming Soon', 'Fund A' => 'Coming Soon' , 'Fund B' => 'Coming Soon' , 'International Fund' => 'Coming Soon'
        });
        
        
        
        //  --------------------------------------------------------------------

        // 3. No of Backed out Investors 

        List<AggregateResult> noOfBackedOutInvestors_FundA = [SELECT COUNT(Id) noOfBackedOutInvestors , SUM(Final_Commitment_Amount__c) backedOutAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1' , 'Friends & Family T2 - AIF') AND Type__c IN ('Back out approved', 'Back out unapproved') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> noOfBackedOutInvestors_FundB = [SELECT COUNT(Id) noOfBackedOutInvestors , SUM(Final_Commitment_Amount__c) backedOutAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1' , 'Friends & Family T2 - AIF') AND Type__c IN ('Back out approved', 'Back out unapproved') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> noOfBackedOutInvestors_FundInt = [SELECT COUNT(Id) noOfBackedOutInvestors ,SUM(Final_Commitment_Amount__c) backedOutAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1' , 'Friends & Family T2 - AIF') AND Type__c IN ('Back out approved', 'Back out unapproved') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal noOfBackedOutInvestorsFundA = (Decimal) noOfBackedOutInvestors_FundA[0].get('noOfBackedOutInvestors') != null ? (Decimal) noOfBackedOutInvestors_FundA[0].get('noOfBackedOutInvestors') : 0;
        Decimal noOfBackedOutInvestorsFundB = (Decimal) noOfBackedOutInvestors_FundB[0].get('noOfBackedOutInvestors') != null ? (Decimal) noOfBackedOutInvestors_FundB[0].get('noOfBackedOutInvestors') : 0;
        Decimal noOfBackedOutInvestorsFundInt = (Decimal) noOfBackedOutInvestors_FundInt[0].get('noOfBackedOutInvestors') != null ? (Decimal) noOfBackedOutInvestors_FundInt[0].get('noOfBackedOutInvestors') : 0;
        Decimal startupRoundAifNoOfBackedOutInvestors = startupRoundList[0].AIF_No_of_Backed_out_Investors__c != null ? startupRoundList[0].AIF_No_of_Backed_out_Investors__c : 0;

        dealTrackerTableMapping.put('No. of Backed out Investors' , new Map<String , Object>{
            'Direct' => startupRoundAifNoOfBackedOutInvestors , 'Fund A' => noOfBackedOutInvestorsFundA , 'Fund B' => noOfBackedOutInvestorsFundB , 'International Fund' => noOfBackedOutInvestorsFundInt
        });    



        //  --------------------------------------------------------------------

        // 4. MGT 14 Amount 

        dealTrackerTableMapping.put('MGT 14 Amount' , new Map<String , Object>{
            'Direct' => 'Coming Soon' , 'Fund A' => 'Coming Soon' , 'Fund B' => 'Coming Soon' , 'International Fund' => 'Coming Soon'
        });    



        //  --------------------------------------------------------------------

        // 5. Actual Commitment Amount 

        List<AggregateResult> actualCommitmentAmount_FundA = [SELECT SUM(Final_Commitment_Amount__c) actualCommitmentAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> actualCommitmentAmount_FundB = [SELECT SUM(Final_Commitment_Amount__c) actualCommitmentAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> actualCommitmentAmount_FundInt = [SELECT SUM(Final_Commitment_Amount__c) actualCommitmentAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal actualCommitmentAmountFundA = (Decimal) actualCommitmentAmount_FundA[0].get('actualCommitmentAmount') != null ? (Decimal) actualCommitmentAmount_FundA[0].get('actualCommitmentAmount') : 0;
        Decimal actualCommitmentAmountFundB = (Decimal) actualCommitmentAmount_FundB[0].get('actualCommitmentAmount') != null ? (Decimal) actualCommitmentAmount_FundB[0].get('actualCommitmentAmount') : 0;
        Decimal actualCommitmentAmountFundInt = (Decimal) actualCommitmentAmount_FundInt[0].get('actualCommitmentAmount') != null ? (Decimal) actualCommitmentAmount_FundInt[0].get('actualCommitmentAmount') : 0;
        Decimal startupRoundAifActualCommitmentAmount = startupRoundList[0].AIF_Actual_Commitment_Amount__c != null ? startupRoundList[0].AIF_Actual_Commitment_Amount__c : 0;

        dealTrackerTableMapping.put('Actual Commitment Amount' , new Map<String , Object>{
            'Direct' => startupRoundAifActualCommitmentAmount.format() , 'Fund A' => actualCommitmentAmountFundA.format() , 'Fund B' => actualCommitmentAmountFundB.format() , 'International Fund' => actualCommitmentAmountFundInt.format()
        });    



        //  --------------------------------------------------------------------

        // 6. Expected Amount 

        List<AggregateResult> expectedAmount_FundA = [SELECT SUM(Investment_Amount_Due__c) expectedAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> expectedAmount_FundB = [SELECT SUM(Investment_Amount_Due__c) expectedAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> expectedAmount_FundInt = [SELECT SUM(Investment_Amount_Due__c) expectedAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal expectedAmountFundA = (Decimal) expectedAmount_FundA[0].get('expectedAmount') != null ? (Decimal) expectedAmount_FundA[0].get('expectedAmount') : 0;
        Decimal expectedAmountFundB = (Decimal) expectedAmount_FundB[0].get('expectedAmount') != null ? (Decimal) expectedAmount_FundB[0].get('expectedAmount') : 0;
        Decimal expectedAmountFundInt = (Decimal) expectedAmount_FundInt[0].get('expectedAmount') != null ? (Decimal) expectedAmount_FundInt[0].get('expectedAmount') : 0;
        Decimal startupRoundAifExpectedAmount = startupRoundList[0].AIF_Expected_Amount__c != null ? startupRoundList[0].AIF_Expected_Amount__c : 0;

        dealTrackerTableMapping.put('Expected Amount' , new Map<String , Object>{
            'Direct' => startupRoundAifExpectedAmount.format() , 'Fund A' => expectedAmountFundA.format() , 'Fund B' => expectedAmountFundB.format() , 'International Fund' => expectedAmountFundInt.format()
        });    



        //  --------------------------------------------------------------------

        // 7. CFM Sent Amount 
        
        List<AggregateResult> cfmSentAmount_FundA = [SELECT SUM(Investment_Amount_Due__c) cfmSentAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND Call_For_Money_Sent__c = true AND Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> cfmSentAmount_FundB = [SELECT SUM(Investment_Amount_Due__c) cfmSentAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND Call_For_Money_Sent__c = true AND Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> cfmSentAmount_FundInt = [SELECT SUM(Investment_Amount_Due__c) cfmSentAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND Call_For_Money_Sent__c = true AND Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal cfmSentAmountFundA = (Decimal) cfmSentAmount_FundA[0].get('cfmSentAmount') != null ? (Decimal) cfmSentAmount_FundA[0].get('cfmSentAmount') : 0;
        Decimal cfmSentAmountFundB = (Decimal) cfmSentAmount_FundB[0].get('cfmSentAmount') != null ? (Decimal) cfmSentAmount_FundB[0].get('cfmSentAmount') : 0;
        Decimal cfmSentAmountFundInt = (Decimal) cfmSentAmount_FundInt[0].get('cfmSentAmount') != null ? (Decimal) cfmSentAmount_FundInt[0].get('cfmSentAmount') : 0;
        Decimal startupRoundAifCfmSentAmount = startupRoundList[0].AIF_CFM_Sent_Amount__c != null ? startupRoundList[0].AIF_CFM_Sent_Amount__c : 0;

        dealTrackerTableMapping.put('CFM Sent Amount' , new Map<String , Object>{
            'Direct' => startupRoundAifCfmSentAmount.format() , 'Fund A' => cfmSentAmountFundA.format() , 'Fund B' => cfmSentAmountFundB.format() , 'International Fund' => cfmSentAmountFundInt.format()
        });    



        //  --------------------------------------------------------------------

        // 8. CFM Yet to Go Amount 

        dealTrackerTableMapping.put('CFM Yet to Go Amount' , new Map<String , Object>{
            'Direct' => 'Coming Soon' , 'Fund A' => 'Coming Soon' , 'Fund B' => 'Coming Soon' , 'International Fund' => 'Coming Soon'
        });    
        


        //  --------------------------------------------------------------------

        // 9. Amount Received from Investors 

        List<AggregateResult> amountReceivedFromInvestors_FundA = [SELECT SUM(Investment_Amount__c) amountReceivedFromInvestors FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> amountReceivedFromInvestors_FundB = [SELECT SUM(Investment_Amount__c) amountReceivedFromInvestors FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> amountReceivedFromInvestors_FundInt = [SELECT SUM(Investment_Amount__c) amountReceivedFromInvestors FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal amountReceivedFromInvestorsFundA = (Decimal) amountReceivedFromInvestors_FundA[0].get('amountReceivedFromInvestors') != null ? (Decimal) amountReceivedFromInvestors_FundA[0].get('amountReceivedFromInvestors') : 0;
        Decimal amountReceivedFromInvestorsFundB = (Decimal) amountReceivedFromInvestors_FundB[0].get('amountReceivedFromInvestors') != null ? (Decimal) amountReceivedFromInvestors_FundB[0].get('amountReceivedFromInvestors') : 0;
        Decimal amountReceivedFromInvestorsFundInt = (Decimal) amountReceivedFromInvestors_FundInt[0].get('amountReceivedFromInvestors') != null ? (Decimal) amountReceivedFromInvestors_FundInt[0].get('amountReceivedFromInvestors') : 0;
        Decimal startupRoundAifAmountReceivedFromInvestors = startupRoundList[0].AIF_Amount_Received_from_Investors__c != null ? startupRoundList[0].AIF_Amount_Received_from_Investors__c : 0;

        dealTrackerTableMapping.put('Amount Received from Investors' , new Map<String , Object>{
            'Direct' => startupRoundAifAmountReceivedFromInvestors.format() , 'Fund A' => amountReceivedFromInvestorsFundA.format() , 'Fund B' => amountReceivedFromInvestorsFundB.format() , 'International Fund' => amountReceivedFromInvestorsFundInt.format()
        });    



        //  --------------------------------------------------------------------

        // 10. Amount Pending from Investors
        
        List<AggregateResult> fnfNoOfInvestor_FundA = [SELECT COUNT(Id) fnfNoOfInvestor FROM Investment__c WHERE Issue_Type__c IN ('Friends & Family T1' , 'Friends & Family T2' , 'IPV FnF - Shadow' , 'IPV HQ - Shadow' ) AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> fnfNoOfInvestor_FundB = [SELECT COUNT(Id) fnfNoOfInvestor FROM Investment__c WHERE Issue_Type__c IN ('Friends & Family T1' , 'Friends & Family T2' , 'IPV FnF - Shadow' , 'IPV HQ - Shadow' ) AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> fnfNoOfInvestor_FundInt = [SELECT COUNT(Id) fnfNoOfInvestor FROM Investment__c WHERE Issue_Type__c IN ('Friends & Family T1' , 'Friends & Family T2' , 'IPV FnF - Shadow' , 'IPV HQ - Shadow' ) AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal fnfNoOfInvestorFundA = (Decimal) fnfNoOfInvestor_FundA[0].get('fnfNoOfInvestor') != null ? (Decimal) fnfNoOfInvestor_FundA[0].get('fnfNoOfInvestor') : 0;
        Decimal fnfNoOfInvestorFundB = (Decimal) fnfNoOfInvestor_FundB[0].get('fnfNoOfInvestor') != null ? (Decimal) fnfNoOfInvestor_FundB[0].get('fnfNoOfInvestor') : 0;
        Decimal fnfNoOfInvestorFundInt = (Decimal) fnfNoOfInvestor_FundInt[0].get('fnfNoOfInvestor') != null ? (Decimal) fnfNoOfInvestor_FundInt[0].get('fnfNoOfInvestor') : 0;

        List<AggregateResult> amountPendingFromInvestors_FundA = [SELECT SUM(Investment_Amount__c) amountPendingFromInvestors FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> amountPendingFromInvestors_FundB = [SELECT SUM(Investment_Amount__c) amountPendingFromInvestors FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> amountPendingFromInvestors_FundInt = [SELECT SUM(Investment_Amount__c) amountPendingFromInvestors FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal amountPendingFromInvestorsFundA = (Decimal) amountPendingFromInvestors_FundA[0].get('amountPendingFromInvestors') != null ? (Decimal) amountPendingFromInvestors_FundA[0].get('amountPendingFromInvestors') : 0;
        Decimal amountPendingFromInvestorsFundB = (Decimal) amountPendingFromInvestors_FundB[0].get('amountPendingFromInvestors') != null ? (Decimal) amountPendingFromInvestors_FundB[0].get('amountPendingFromInvestors') : 0;
        Decimal amountPendingFromInvestorsFundInt = (Decimal) amountPendingFromInvestors_FundInt[0].get('amountPendingFromInvestors') != null ? (Decimal) amountPendingFromInvestors_FundInt[0].get('amountPendingFromInvestors') : 0;
        Decimal startupRoundAifAmountPendingFromInvestors = startupRoundList[0].AIF_Amount_Pending_from_Investors__c != null ? startupRoundList[0].AIF_Amount_Pending_from_Investors__c : 0;


        dealTrackerTableMapping.put('Amount Pending from Investors' , new Map<String , Object>{
            'Direct' => startupRoundAifAmountPendingFromInvestors.format() , 'Fund A' => (fnfNoOfInvestorFundA - amountPendingFromInvestorsFundA).format() , 'Fund B' => (fnfNoOfInvestorFundB - amountPendingFromInvestorsFundB).format() , 'International Fund' => (fnfNoOfInvestorFundInt - amountPendingFromInvestorsFundInt).format()
        });    



        //  --------------------------------------------------------------------

        // 11. Amount Pending to Startup

        Decimal startupRoundAifAmountPendingToStartup = startupRoundList[0].AIF_Amount_Pending_to_Startup__c != null ? startupRoundList[0].AIF_Amount_Pending_to_Startup__c : 0;
        Decimal aifAmountTransferredToStartup = startupRoundList[0].AIF_Amount_Transferred_to_Startup__c != null ? startupRoundList[0].AIF_Amount_Transferred_to_Startup__c : 0;


        dealTrackerTableMapping.put('Amount Pending to Startup' , new Map<String , Object>{
            'Direct' => startupRoundAifAmountPendingToStartup.format() , 'Fund A' => (fnfNoOfInvestorFundA - aifAmountTransferredToStartup).format() , 'Fund B' => (fnfNoOfInvestorFundB - aifAmountTransferredToStartup).format() , 'International Fund' => (fnfNoOfInvestorFundInt - aifAmountTransferredToStartup).format()
        });  



        //  --------------------------------------------------------------------

        // 12. Amount Transferred to Startup

        dealTrackerTableMapping.put('Amount Transferred to startup' , new Map<String , Object>{
            'Direct' => 'Coming Soon' , 'Fund A' => 'Coming Soon' , 'Fund B' => 'Coming Soon' , 'International Fund' => 'Coming Soon'
        });    



        //  --------------------------------------------------------------------

        // 13. Waitlist Amount 
        
        List<AggregateResult> waitlistAmount_FundA = [SELECT SUM(Investment_Amount_Due__c) waitlistAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund A'];

        List<AggregateResult> waitlistAmount_FundB = [SELECT SUM(Investment_Amount_Due__c) waitlistAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'Fund B'];

        List<AggregateResult> waitlistAmount_FundInt = [SELECT SUM(Investment_Amount_Due__c) waitlistAmount FROM Investment__c WHERE Issue_Type__c IN ('Primary - AIF' , 'Friends & Family T1 - AIF' , 'Friends & Family T2 - AIF') AND Type__c IN ('Invested', 'Committed') AND  Startup_Round__c = :startupRoundId AND Fund_Type__r.Name = 'International Fund'];

        Decimal waitlistAmountFundA = (Decimal) waitlistAmount_FundA[0].get('waitlistAmount') != null ? (Decimal) waitlistAmount_FundA[0].get('waitlistAmount') : 0;
        Decimal waitlistAmountFundB = (Decimal) waitlistAmount_FundB[0].get('waitlistAmount') != null ? (Decimal) waitlistAmount_FundB[0].get('waitlistAmount') : 0;
        Decimal waitlistAmountFundInt = (Decimal) waitlistAmount_FundInt[0].get('waitlistAmount') != null ? (Decimal) waitlistAmount_FundInt[0].get('waitlistAmount') : 0;
        Decimal startupRoundAifWaitlistAmount = startupRoundList[0].AIF_Waitlist_Amount__c != null ? startupRoundList[0].AIF_Waitlist_Amount__c : 0;

        dealTrackerTableMapping.put('Waitlist Amount' , new Map<String , Object>{
            'Direct' => startupRoundAifWaitlistAmount.format() , 'Fund A' => waitlistAmountFundA.format() , 'Fund B' => waitlistAmountFundB.format() , 'International Fund' => waitlistAmountFundInt.format()
        });    



        //  --------------------------------------------------------------------

        // 14. Buffer Amount 
        Decimal startupRoundAifBufferAmount = startupRoundList[0].AIF_Buffer_Amount__c != null ? startupRoundList[0].AIF_Buffer_Amount__c : 0;
        Decimal aifMgt14Amount = startupRoundList[0].AIF_MGT_14_Amount__c != null ? startupRoundList[0].AIF_MGT_14_Amount__c : 0;

        dealTrackerTableMapping.put('Buffer Amount' , new Map<String , Object>{
            'Direct' => startupRoundAifBufferAmount.format() , 'Fund A' => (fnfNoOfInvestorFundA - aifMgt14Amount).format() , 'Fund B' => (fnfNoOfInvestorFundB - aifMgt14Amount).format() , 'International Fund' => (fnfNoOfInvestorFundInt - aifMgt14Amount).format()
        });    



        
        //  --------------------------------------------------------------------

        // 15. Backed out Amounts 

        Decimal backedOutAmountFundA = (Decimal) noOfBackedOutInvestors_FundA[0].get('backedOutAmount') != null ? (Decimal) noOfBackedOutInvestors_FundA[0].get('backedOutAmount') : 0;
        Decimal backedOutAmountFundB = (Decimal) noOfBackedOutInvestors_FundB[0].get('backedOutAmount') != null ? (Decimal) noOfBackedOutInvestors_FundB[0].get('backedOutAmount') : 0;
        Decimal backedOutAmountFundInt = (Decimal) noOfBackedOutInvestors_FundInt[0].get('backedOutAmount') != null ? (Decimal) noOfBackedOutInvestors_FundInt[0].get('backedOutAmount') : 0;
        Decimal startupRoundAifBackedOutAmounts = startupRoundList[0].AIF_Backed_out_Amounts__c != null ? startupRoundList[0].AIF_Backed_out_Amounts__c : 0;

        dealTrackerTableMapping.put('Backed out Amounts' , new Map<String , Object>{
            'Direct' => startupRoundAifBackedOutAmounts.format() , 'Fund A' => backedOutAmountFundA.format() , 'Fund B' => backedOutAmountFundB.format() , 'International Fund' => backedOutAmountFundInt.format()
        });    



        //  --------------------------------------------------------------------

        System.debug('Final Size of Map is ::: ' + dealTrackerTableMapping.size());
        
    }

        return dealTrackerTableMapping;
        
    }
}