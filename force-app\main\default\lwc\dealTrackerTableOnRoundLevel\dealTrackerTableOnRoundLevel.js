import { LightningElement, wire, track, api } from 'lwc';
import getDealTrackerTableData from '@salesforce/apex/DealTrackerTableOnStartupRound.dealTrackerTableDetails';

export default class DealTrackerTableOnRoundLevel extends LightningElement {
    @api recordId;
    @track columns = [];
    @track rows = [];

    @wire(getDealTrackerTableData, { startupRoundId: '$recordId' })
    wiredData({ error, data }) {
        if (data) {
            console.log('✅ Raw Apex Data:', JSON.stringify(data));

            const firstRow = Object.values(data)[0];
            if (!firstRow) {
                console.warn('⚠ No table data found.');
                return;
            }

            // Get fund headers from the first row
            const fundHeaders = Object.keys(firstRow);
            this.columns = ['Details', ...fundHeaders];

            // Transform into row objects with unique IDs for each cell
            this.rows = Object.keys(data).map(detailLabel => {
                let rowData = {
                    id: detailLabel, // row unique id
                    cells: []
                };

                this.columns.forEach((col, idx) => {
                    let value = col === 'Details' ? detailLabel : data[detailLabel][col];
                    rowData.cells.push({
                        id: `${detailLabel}-${idx}`, // cell unique id
                        value: value
                    });
                });

                return rowData;
            });

            console.log('✅ Processed Rows:', JSON.stringify(this.rows));
        } else if (error) {
            console.error('❌ Apex Error:', JSON.stringify(error));
        }
    }
}