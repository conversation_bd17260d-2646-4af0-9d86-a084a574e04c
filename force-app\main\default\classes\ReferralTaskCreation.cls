public class ReferralTaskCreation {
    public static void taskCreationOnAccountInsert(List<Account>AccLst){
        list<Task> TaskLst =new list<Task>();
        if(!AccLst.isempty()){
            for (Account  Acc:AccLst){
                if(Acc.Date_of_1st_Payment__c != null) {
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Referral Nudge Onboarding Call';
                    tsk.Subject ='Refferal Trigger Onboarding Call Nudge';
                    tsk.OwnerId =Acc.Relationship_Manager__c;
                    tsk.WhatId = Acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task>>>>'+tsk);
                } 
                if(Acc.Account_Premier_Status__c == 'Onboarded on Premier +'  || 	Acc.Account_Premier_Status__c =='Onboarded on Premier') {
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Referral Nudge Premier Select Onboarding';
                    tsk.Subject ='Refferal Trigger Onboarded to Premier Select';
                    tsk.OwnerId =Acc.Relationship_Manager__c;
                    tsk.WhatId = Acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task>>>>'+tsk);
                }
                if(Acc.Contributor_to_AIF__c == 'Yes') {
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Referral Nudge AIF Onboarding';
                    tsk.Subject ='Refferal Trigger Onboarded to AIF';
                    tsk.OwnerId =Acc.Relationship_Manager__c;
                    tsk.WhatId = Acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task>>>>'+tsk);
                }
                if(Acc.IPV_International_Status__c == 'Onboarded on IPV International') {
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Referral Nudge IPV International';
                    tsk.Subject ='Refferal Trigger Onboarded to IPV International';
                    tsk.OwnerId =Acc.Relationship_Manager__c;
                    tsk.WhatId = Acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task>>>>'+tsk);
                } 
              /*  if(Acc.Membership_Slab__c != null){
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Slab upgraded to Silver/Gold';
                    tsk.Subject ='Refferal Trigger';
                    tsk.OwnerId =Acc.Relationship_Manager__c;
                    tsk.WhatId = Acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task for Membership Slab>>>>'+tsk);
                }
                if(Acc.Date_of_Payment__c != null && (Acc.Payment_Type__c == 'Early Renewal' || Acc.Payment_Type__c == 'Renewal - Due date expired'|| Acc.Payment_Type__c  == 'Renewal')){
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Membership Renewal';
                    tsk.Subject ='Refferal Trigger';
                    tsk.OwnerId =Acc.Relationship_Manager__c;
                    tsk.WhatId = Acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task for Membership Slab>>>>'+tsk);
                }*/
            }  
            insert TaskLst;
        }
    }
    public static void taskCreationOnAccountUpdate(List<Account>AccLst,Map<Id,Account>oldaccountMap){
        list<Task> TaskLst =new list<Task>();
        Set<Id> accSet = new Set<Id>();
        If(!AccLst.Isempty()){
            for(Account acc:AccLst){
                accSet.Add(acc.Id);
            }
        }
        
        Map<Id,set<String>> accHasTsk =new Map<Id,set<String>>();
        for (Task t:[select Referral_Trigger_Status__c,Referral_Trigger_Type__c,Subject,WhatId   From Task Where WhatId =:accSet
                     AND Referral_Trigger_Type__c In  ('Referral Nudge IPV International','Referral Nudge AIF Onboarding','Referral Nudge Premier Select Onboarding','Referral Nudge Onboarding Call')
                     AND Referral_Trigger_Status__c ='Pending'
                     AND Subject ='Refferal Trigger'
                    ]){
                        if (!accHasTsk.containsKey(t.WhatId)) {
                            accHasTsk.put(t.WhatId, new Set<String>());
                        }
                        accHasTsk.get(t.WhatId).add(t.Referral_Trigger_Type__c);
                    }
        System.debug('AccHasTask>>>'+accHasTsk);        
        System.debug('Account Task ::: '+accHasTsk.size());
        if(!AccLst.isempty()){
            for(Account acc:AccLst){
                if(oldaccountMap.get(acc.Id).Date_of_1st_Payment__c == Null && acc.Date_of_1st_Payment__c !=Null && ( !accHasTsk.containsKey(acc.Id) ||  (accHasTsk.containsKey(acc.Id) && !accHasTsk.get(acc.Id).contains('Referral Nudge Onboarding Call')))){
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Referral Nudge Onboarding Call';
                    tsk.Subject ='Refferal Trigger Onboarding Call Nudge';
                    tsk.OwnerId =acc.Relationship_Manager__c;
                    tsk.WhatId = acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task>>>>'+tsk);
                }
                System.debuG('Premier Started>>>');
                if (
                    (acc.Account_Premier_Status__c == 'Onboarded on Premier +' ||
                     acc.Account_Premier_Status__c == 'Onboarded on Premier') &&
                    (oldaccountMap.get(acc.Id).Account_Premier_Status__c != 'Onboarded on Premier +' &&
                     oldaccountMap.get(acc.Id).Account_Premier_Status__c != 'Onboarded on Premier') &&
                    (!accHasTsk.containsKey(acc.Id) ||
                     !accHasTsk.get(acc.Id).contains('Referral Nudge Premier Select Onboarding'))
                ) 
                    
                {
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Referral Nudge Premier Select Onboarding';
                    tsk.Subject ='Refferal Trigger Onboarded to Premier Select';
                    tsk.OwnerId =acc.Relationship_Manager__c;
                    tsk.WhatId = acc.Id;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task for premier>>>>'+tsk);
                    
                }
                 System.debuG('Premier end>>>');
                if(Acc.Contributor_to_AIF__c == 'Yes' &&
                   oldaccountMap.get(acc.Id).Contributor_to_AIF__c != 'Yes' && (!accHasTsk.containsKey(acc.Id) ||  (accHasTsk.containsKey(acc.Id) && !accHasTsk.get(acc.Id).contains('Referral Nudge AIF Onboarding')))){
                       Task tsk =new Task();
                       tsk.Referral_Trigger_Status__c='Pending';
                       tsk.Referral_Trigger_Type__c = 'Referral Nudge AIF Onboarding';
                       tsk.Subject ='Refferal Trigger Onboarded to AIF';
                       tsk.OwnerId =acc.Relationship_Manager__c;
                       tsk.WhatId = acc.Id;
                       tsk.ActivityDate =System.today().AddDays(+2);
                       TaskLst.add(tsk);
                       system.debug('Task for AIf>>>>'+tsk);
                   }
                if(Acc.IPV_International_Status__c == 'Onboarded on IPV International' &&  oldaccountMap.get(acc.Id).IPV_International_Status__c != 'Onboarded on IPV International' &&
                   ( !accHasTsk.containsKey(acc.Id) ||  (accHasTsk.containsKey(acc.Id) && !accHasTsk.get(acc.Id).contains('Referral Nudge IPV International')))) {
                       Task tsk =new Task();
                       tsk.Referral_Trigger_Status__c='Pending';
                       tsk.Referral_Trigger_Type__c = 'Referral Nudge IPV International';
                       tsk.Subject ='Refferal Trigger Onboarded to IPV International';
                       tsk.OwnerId =acc.Relationship_Manager__c;
                       tsk.WhatId = acc.Id;
                       tsk.ActivityDate =System.today().AddDays(+2);
                       TaskLst.add(tsk);
                       system.debug('Task>>>>'+tsk);
                   } 
                Map<String, Integer> membershipHierarchy = new Map<String, Integer>{
                    'Gold' => 3,
                        'Silver' => 2,
                        'Bronze' => 1
                        };   
                            if(oldaccountMap.get(acc.Id) != null && acc.Membership_Slab__c != oldaccountMap.get(acc.Id).Membership_Slab__c){
                                if((membershipHierarchy.get(acc.Membership_Slab__c) > membershipHierarchy.get(oldaccountMap.get(acc.Id).Membership_Slab__c))){
                                    Task tsk =new Task();
                                    tsk.Referral_Trigger_Status__c='Pending';
                                    tsk.Referral_Trigger_Type__c = 'Slab upgraded to Silver/Gold';
                                    tsk.Subject ='Refferal Trigger Slab Updation';
                                    tsk.OwnerId =acc.Relationship_Manager__c;
                                    tsk.WhatId = acc.Id;
                                    tsk.ActivityDate =System.today().AddDays(+2);
                                    TaskLst.add(tsk);
                                    system.debug('Task>>>>'+tsk);
                                }
                            }
                if(acc.Date_of_Payment__c != null && (acc.Payment_Type__c == 'Early Renewal' || acc.Payment_Type__c == 'Renewal - Due date expired'|| acc.Payment_Type__c  == 'Renewal') ){
                    IF(acc.Date_of_Payment__c != oldaccountMap.get(acc.Id).Date_of_Payment__c  ){
                        Task tsk =new Task();
                        tsk.Referral_Trigger_Status__c='Pending';
                        tsk.Referral_Trigger_Type__c = 'Membership Renewal';
                        tsk.Subject ='Refferal Trigger Renewed Membership';
                        tsk.OwnerId =Acc.Relationship_Manager__c;
                        tsk.WhatId = Acc.Id;
                        tsk.ActivityDate =System.today().AddDays(+2);
                        TaskLst.add(tsk);
                        system.debug('Task for Membership Slab>>>>'+tsk);
                    }
                }
                
            }
        }
        insert TaskLst;
        system.Debug('Task inserted succesfully'+TaskLst);
    }
    
    public static void taskCreationOnInvestmentInsert(List<Investment__c>invLst){
        list<Task> TaskLst = new List<Task>();
        Set<Id> invIdSet = new Set<Id>();
      
        if(!invLst.isempty()){
            for(Investment__c  inv:invLst){
                if(inv.Account__c !=null){
                    invIdSet.add(inv.Account__c);  
                }
               
            }
        }
        
        Map<Id, Set<String>> existingTaskMap = new Map<Id, Set<String>>();
        for (Task t : [
            SELECT WhatId, Referral_Trigger_Type__c 
            FROM Task 
            WHERE Referral_Trigger_Status__c != null 
            AND WhatId IN :invIdSet
            AND Subject = 'Referral Trigger'
        ]) {
            if (!existingTaskMap.containsKey(t.WhatId)) {
                existingTaskMap.put(t.WhatId, new Set<String>());
            }
            existingTaskMap.get(t.WhatId).add(t.Referral_Trigger_Type__c);
        }

        

        invLst = [SELECT Id , Account__r.Relationship_Manager__r.Id,Type__c FROM Investment__c WHERE Id IN : invLst];
        Map<Id,Integer> InvestmentCount  = new Map<Id,Integer>();
        List<AggregateResult> Result =[select Count(Id) Cnt,Account__c acct From Investment__C where Account__c IN : invIdSet AND Type__c ='Invested'  group By Account__c ];
        for(AggregateResult ar:Result){
            Integer Count =(Integer)ar.Get('Cnt');
            Id AccID =(ID)ar.get('acct');
            InvestmentCount.put(AccID,Count);
        }
        
        for(Investment__c inv:invLst){
             Set<String> existingTriggers = existingTaskMap.get(inv.Id);
            Integer Count =InvestmentCount.get(inv.Account__c);
            if(existingTriggers == null || !existingTriggers.contains('Member invested in 3 or more deals')){
            if(Count !=null && Count > 0 && Math.Mod(Count,3)==0){
                if(inv.Type__c != null ){
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Member invested in 3 or more deals';
                    tsk.Subject ='	Refferal Trigger 3+ Investments Made';
                    tsk.OwnerId =inv.Account__r.Relationship_Manager__r.Id;
                    tsk.WhatId = inv.Account__c;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task>>>>'+tsk);
                } 

            }
            }
        }
        
        
        
        map<Id,Boolean> hasInvCheak = new Map<Id,Boolean>();
        for(Investment__c I:[select Account__c,Type__c  From Investment__C where Type__c IN ('Invested','Exit','Partial Exit','Internal Transfer')]){
            hasInvCheak.put(I.Account__c,True);
        }
        
        for(Investment__c inv:invLst){
            if(!invIdSet.contains(inv.Account__c)) continue;
            If(hasInvCheak.containskey(inv.Account__c)){
                system.debug('This is not first investment>>>>>');
                continue;
                
            }
            Set<String> existingTriggers = existingTaskMap.get(inv.Id);
            if(existingTriggers == null || !existingTriggers.contains('Referral Nudge First Investment')){
            if(inv.Type__c != null ){
                Task tsk =new Task();
                tsk.Referral_Trigger_Status__c='Pending';
                tsk.Referral_Trigger_Type__c = 'Referral Nudge First Investment';
                tsk.Subject ='Refferal Trigger First Investment Made';
                tsk.OwnerId =inv.Account__r.Relationship_Manager__r.Id;
                tsk.WhatId = inv.Account__c;
                tsk.ActivityDate =System.today().AddDays(+2);
                TaskLst.add(tsk);
                system.debug('Task>>>>'+tsk);
            }
            
        }
        }
       
        insert TaskLst;
        
        
        
    }
    public static void taskCreationOnInvestmentUpdate(List<Investment__c> newList, Map<Id, Investment__c> oldMap) {
        List<Task> taskLst = new List<Task>();
        Set<Id> accountIds = new Set<Id>();
        Set<Id> updatedInvestmentIds = new Set<Id>();
        seT<Id> InvestorID=new Set<Id>();
        
        newList = [SELECT Id , Account__r.Relationship_Manager__r.Id,Type__c,Investor__c FROM Investment__c WHERE Id IN : newList];
        
        for (Investment__c newInv : newList) {
            Investment__c oldInv = oldMap.get(newInv.Id);
            
            if(newInv.Investor__c != null){ 
                 InvestorID.Add(newInv.Investor__c);
                       system.debug('Invstor id'+InvestorID);
                }  
            if (newInv.Account__c != null &&
                newInv.Type__c != oldInv.Type__c &&
                newInv.Type__c != null &&
                newInv.Type__c == 'Invested'){
                    accountIds.add(newInv.Account__c);
                    updatedInvestmentIds.add(newInv.Id);
                   
                      
                }
        }
         
        Map<Id, Set<String>> existingTaskMap = new Map<Id, Set<String>>();
        for (Task t : [
            SELECT WhatId, Referral_Trigger_Type__c 
            FROM Task 
            WHERE Referral_Trigger_Status__c != null 
            AND WhatId IN :updatedInvestmentIds
            AND Subject = 'Referral Trigger'
        ]) {
            if (!existingTaskMap.containsKey(t.WhatId)) {
                existingTaskMap.put(t.WhatId, new Set<String>());
            }
            existingTaskMap.get(t.WhatId).add(t.Referral_Trigger_Type__c);
        }
        
        if (accountIds.isEmpty()) return;
        
        Map<Id,Integer> InvestmentCount  = new Map<Id,Integer>();
        List<AggregateResult> Result =[select Count(Id) Cnt,Account__c acct From Investment__C where Account__c IN : accountIds AND Type__c ='Invested'  group By Account__c ];
        for(AggregateResult ar:Result){
            Integer Count =(Integer)ar.Get('Cnt');
            Id AccID =(ID)ar.get('acct');
            InvestmentCount.put(AccID,Count);
        }
        
        for(Investment__c inv:newList){
            Integer Count =InvestmentCount.get(inv.Account__c);
            Set<String> existingTriggers = existingTaskMap.get(inv.Id);
            System.debug('RM Id :::: ' + inv.Account__r.Relationship_Manager__r.Id);
             if(existingTriggers == null || !existingTriggers.contains('Member invested in 3 or more deals')){
            if(Count !=null && Count > 0 && Math.Mod(Count,3)==0){
                if(inv.Type__c != null ){
                    Task tsk =new Task();
                    tsk.Referral_Trigger_Status__c='Pending';
                    tsk.Referral_Trigger_Type__c = 'Member invested in 3 or more deals';
                    tsk.Subject ='Refferal Trigger 3+ Investments Made';
                    tsk.OwnerId =inv.Account__r.Relationship_Manager__r.Id;
                    tsk.WhatId = inv.Account__c;
                    tsk.ActivityDate =System.today().AddDays(+2);
                    TaskLst.add(tsk);
                    system.debug('Task>>>>'+tsk);
                } 
            }
             }
        


        }
        
        Set<Id> accountsWithOtherInvestments = new Set<Id>();
        for (Investment__c inv : [
            SELECT Account__c
            FROM Investment__c
            WHERE Account__c IN :accountIds
            AND Id NOT IN :updatedInvestmentIds
            AND Type__c IN ('Invested', 'Exit', 'Partial Exit', 'Internal Transfer')
        ]) {
            accountsWithOtherInvestments.add(inv.Account__c);
        }
        
        
        for (Investment__c inv : newList) {
            if (!updatedInvestmentIds.contains(inv.Id)) continue;
            
            if (accountsWithOtherInvestments.contains(inv.Account__c)) {
                System.debug('Other investments exist for Account: ' + inv.Account__c);
                continue;
            }
            
            Task tsk = new Task();
            tsk.Referral_Trigger_Status__c = 'Pending';
            tsk.Referral_Trigger_Type__c = 'Referral Nudge First Investment';
            tsk.Subject = '	Refferal Trigger First Investment Made';
            tsk.OwnerId = inv.Account__r.Relationship_Manager__r.Id;
            tsk.WhatId = inv.Account__c;
            tsk.ActivityDate = System.today().addDays(2);
            taskLst.add(tsk);
            System.debug('Task created on update for first investment: ' + tsk);
        }
        
        
        if (!taskLst.isEmpty()) {
            insert taskLst;
        }
    }
    public static void taskCreationOnCaseResolve(List<Case> newList, Map<Id, Case> oldMap) {
        List<Task> taskLst = new List<Task>();
        if(!newList.isempty()){
            for(Case cs:newList){
                if((oldMap.get(cs.Id)).Status != cs.Status && cs.Complaint_Type__c != Null)
                if(cs.Complaint_Type__c=='Complaint' && cs.Status=='Closed'){
                     Task tsk = new Task();
                     tsk.Referral_Trigger_Status__c = 'Pending';
                     tsk.Referral_Trigger_Type__c = 'Case Resolution Type – Complaint';
                     tsk.Subject = 'Refferal Trigger Complaint Resolved';
                     tsk.OwnerId =cs.Responsibility_to_Solve__c;
                     tsk.WhatId = cs.Issue_raised_By__c;
                     tsk.ActivityDate = System.today().addDays(2);
                     taskLst.add(tsk);
                    System.debug('Task created on Case resolved: ' + tsk);

                }
            }
        }
        insert taskLst;
        
    }
     private static Boolean isTaskCreated = false;
    public static void checkMilestoneCrossing(Set<Id> investmentIds) {
         
    if (investmentIds.isEmpty()  || isTaskCreated)  return;

  
    List<Investment__c> updatedInvestments = [
        SELECT Id, Investor__c, Investment_Amount__c, Type__c, Startup_Round__c,
               Fund_Type__r.Name, Account__c, Account__r.Relationship_Manager__c,Currency__c
        FROM Investment__c
        WHERE Id IN :investmentIds
    ];

    Set<Id> roundIds = new Set<Id>();
    Set<Id> investorIds = new Set<Id>();
    for (Investment__c inv : updatedInvestments) {
        if (inv.Startup_Round__c != null) roundIds.add(inv.Startup_Round__c);
        if (inv.Investor__c != null) investorIds.add(inv.Investor__c);
    }

   
    Map<Id, Startup_Round__c> startupRounds = new Map<Id, Startup_Round__c>(
        [SELECT Id, Round_Type__c FROM Startup_Round__c WHERE Id IN :roundIds]
    );
    
   
    Decimal conversionRate = 0;
    List<International_Currency_For_Calculation__mdt> metaRates = [
        SELECT USD__c FROM International_Currency_For_Calculation__mdt LIMIT 1
    ];
    if (!metaRates.isEmpty()) {
        conversionRate = metaRates[0].USD__c;
    }
    if (conversionRate == 0) return;

   
    List<Investment__c> allRelatedInvestments = [
        SELECT Id, Investor__c, Investment_Amount__c, Type__c, Startup_Round__c,
               Fund_Type__r.Name, Account__c, Account__r.Relationship_Manager__c,Currency__c
        FROM Investment__c
        WHERE Investor__c IN :investorIds AND Investment_Amount__c != null
    ];
   
   
    Map<Id, List<Investment__c>> investorAllInvestments = new Map<Id, List<Investment__c>>();
    for (Investment__c inv : allRelatedInvestments) {
        if (!investorAllInvestments.containsKey(inv.Investor__c)) {
            investorAllInvestments.put(inv.Investor__c, new List<Investment__c>());
        }
        investorAllInvestments.get(inv.Investor__c).add(inv);
    }

    List<Task> tasksToCreate = new List<Task>();

    for (Id investorId : investorIds) {
        List<Investment__c> investments = investorAllInvestments.get(investorId);
        if (investments == null) continue;

        Decimal totalInr = 0;
        Decimal latestChangeInr = 0;
        Investment__c sampleInvestment = null;

        for (Investment__c inv : investments) {
            if (inv.Investment_Amount__c == null) continue;

            Startup_Round__c round = startupRounds.get(inv.Startup_Round__c);
            String roundType = (round != null) ? round.Round_Type__c : null;

            if (roundType != 'Raise' && roundType != 'Internal Transfer') continue;
            if (inv.Type__c != 'Invested') continue;

            Boolean isUSD = (inv.Fund_Type__r != null && inv.Fund_Type__r.Name == 'International Fund' &&  inv.Currency__c=='USD');
            Decimal amountInInr = isUSD 
                ? inv.Investment_Amount__c * conversionRate 
                : inv.Investment_Amount__c;

            totalInr += amountInInr;

            if (investmentIds.contains(inv.Id)) {
                latestChangeInr += amountInInr;
                if (sampleInvestment == null) {
                    sampleInvestment = inv;
                }
            }
        }

        Integer previousMilestone = (Integer)Math.floor((totalInr - latestChangeInr) / 1000000);
        Integer currentMilestone = (Integer)Math.floor(totalInr / 1000000);

        if (currentMilestone > previousMilestone && sampleInvestment != null && sampleInvestment.Account__r != null) {
            Task tsk = new Task();
            tsk.Referral_Trigger_Status__c = 'Pending';
            tsk.Referral_Trigger_Type__c = 'Memberʼs AUM increased by INR 10 Lakhs';
            tsk.Subject = 'Refferal Trigger AUM increased by 10 lakhs';
            tsk.OwnerId = sampleInvestment.Account__r.Relationship_Manager__c;
            tsk.WhatId = sampleInvestment.Account__c;
            tsk.ActivityDate = System.today().addDays(2);
            tasksToCreate.add(tsk);
            system.debug('TAsk For 10 lakh is Created >>>>'+tsk);
        }
        
    }

   
    if (!tasksToCreate.isEmpty()) {
        insert tasksToCreate;
        isTaskCreated = true;
    }
}

   
    
}