import { LightningElement, wire, api } from 'lwc';
import getlistName from '@salesforce/apex/ReferralTaskTableController.ReferralTaskTable';
import getReferralWithTrigger from '@salesforce/apex/ReferralTaskTableController.ReferralWithTrigger';
import getReferralWithoutTrigger from '@salesforce/apex/ReferralTaskTableController.ReferralWithoutTrigger';

export default class ReferralTable extends LightningElement {
    @api recordId;

    TaskData = [];
    referralWithTrigger = {};
    referralWithoutTrigger = {};
    error;

    totalWithTriggers = 0;
    totalWithoutTriggers = 0;

    get objectKeys() {
        return Object.keys(this.referralWithTrigger || {});
    }

    get objectKeysWithout() {
        return Object.keys(this.referralWithoutTrigger || {});
    }

    
    @wire(getlistName, { accountId: '$recordId' })
    wiredTaskData({ error, data }) {
        if (data) {
            this.TaskData = data.map(item => ({
                ...item,
                CreatedDateFormatted: new Intl.DateTimeFormat('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                }).format(new Date(item.CreatedDate))
            }));
        } else if (error) {
            this.error = error;
            this.TaskData = [];
        }
        console.log(this.TaskData);
    }

   
    @wire(getReferralWithTrigger, { recordId: '$recordId' })
    wiredWithTriggers({ error, data }) {
        if (data) {
            this.referralWithTrigger = data;
            this.totalWithTriggers = Object.values(data).reduce((sum, val) => sum + val, 0);
        } else if (error) {
            this.error = error;
            this.referralWithTrigger = {};
        }
    }

    // Get Referrals WITHOUT Triggers
    @wire(getReferralWithoutTrigger, { recordId: '$recordId' })
    wiredWithoutTriggers({ error, data }) {
        if (data) {
            this.referralWithoutTrigger = data;
            this.totalWithoutTriggers = Object.values(data).reduce((sum, val) => sum + val, 0);
        } else if (error) {
            this.error = error;
            this.referralWithoutTrigger = {};
        }
    }
}