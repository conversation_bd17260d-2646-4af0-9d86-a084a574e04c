//-------------------------------------Class Is Currently Under Development-------------------------------------------------------

@RestResource(urlMapping='/CreateSecondaryRequestRecordAPI/*')
Global with sharing class CreateSecondaryRequestRecordAPI {
    
    private static String CLASS_NAME = 'CreateSecondaryRequestRecordAPI';
    @HttpPost
    global Static ResponseWrapper createSecondaryRequestRecord()
    {
        responseWrapper wResponse = new responseWrapper();
        try{
            RestRequest req = RestContext.request;
            RestResponse res = Restcontext.response;
            string jsonReqString=req.requestBody.tostring();
            List<Secondary_Module__c> secondaryReqInsertList = new List<Secondary_Module__c>();
            
            System.Debug('requestWrapper>>>>>>>>>>>>>' + jsonReqString);
            requestWrapper wResp=(requestWrapper) JSON.deserialize(jsonReqString,requestWrapper.class);
            System.Debug('wResp>>>>>>>>>>>>>' + wResp);
            
            If(wResp!=null && wResp.dataList != null)
            {
                for(SecondaryModuleRequestWrapper smrw : wResp.dataList)
                {
                    Secondary_Module__c smr = new Secondary_Module__c();
                    
                    if(smrw.request_at != null){
                        smr.Date_Time_of_Filling_the_Form__c = smrw.request_at;
                    }

                    if(smrw.salesforce_user_account_id != null ){
                        smr.Member_Name__c = smrw.salesforce_user_account_id;
                    }

                    if(smrw.salesforce_investor_id != null){
                        smr.Investor_Name__c = smrw.salesforce_investor_id;
                    }

                    if(smrw.startup_id != null){
                        smr.Startup__c = smrw.startup_id;
                    }

                    if(smrw.startup_round_id != null){
                        smr.Startup_Round__c = smrw.startup_round_id;
                    }

                    if(smrw.bid_price != null){
                        smr.Bid_Price__c = smrw.bid_price;
                    }

                    if(smrw.commitment_request_amount != null){
                        smr.Commitment_Request_Amount__c = smrw.commitment_request_amount;
                    }

                    if(smrw.compliance_consent_t_c != null){
                        smr.Compliance_Consent_T_C__c = smrw.compliance_consent_t_c;
                    }
                    
                    if(smrw.app_secondary_investment_id != null ){
                        smr.App_Secondary_Iinvestment_ID__c = smrw.app_secondary_investment_id; 
                    }
                    //Added by bharat for selling intent requirement on 28-07-2025
                    if(smrw.type_of_intent == 1){
                        smr.Type_of_Intent__c = 'Buying Intent';
                    }else if(smrw.type_of_intent == 2){
                        smr.Type_of_Intent__c = 'Selling Intent';
                    }
                    
                    if(smrw.no_of_shares_invested != null){
                        smr.Number_of_shares_invested__c = smrw.no_of_shares_invested;
                    }
                    
                    if(smrw.no_of_shares_wish_to_sell != null){
                        smr.Number_of_shares_wish_to_sell__c = smrw.no_of_shares_wish_to_sell;
                    }
                    
                    if(smrw.price_per_share_wish_to_sell != null){
                        smr.Price_per_share_wish_to_sell_at__c = smrw.price_per_share_wish_to_sell;
                    }
                    
                    if(smrw.total_invested_amount != null){
                        smr.Total_amount_invested__c = smrw.total_invested_amount;
                    }
                    
                    if(smrw.salesforce_investment_id != null){
                        smr.Investment__c = smrw.salesforce_investment_id;
                    }
                    
                    secondaryReqInsertList.add(smr);
                }
                if(secondaryReqInsertList != null && secondaryReqInsertList.size() > 0)
                {
                    insert secondaryReqInsertList;
                    for(Secondary_Module__c sm : secondaryReqInsertList)
                    {
                        System.debug('NEW SECONDARY REQUEST ID>>>>>'+sm.Id);
                        System.debug('NEW SECONDARY REQUEST Number>>>>>'+ sm.Name);
                        System.debug('NEW SECONDARY REQUEST>>>>>'+sm);
                    }
                }
                wResponse.status = true;
                wResponse.message= 'Secondary Request records are created successfully';
            }
        }
        catch(exception e)
        {
            wResponse.status = false;
            wResponse.message= 'Exception:'+e.getMessage();
            system.debug('Error '+e);
            Error_Log__c log =  new Error_Log__c();
            log.Class_Name__c = CLASS_NAME;
            log.method__c = 'createSecondaryRequestRecord' ;
            log.Error_Message__c = e.getMessage();
            insert log;
        }
        return wResponse;
    }
    
    global class SecondaryModuleRequestWrapper{
        
        global DateTime request_at;
        global String salesforce_user_account_id;
        global String salesforce_investor_id;
        global String contact;
        global String startup_id;
        global String startup_round_id;
        global Decimal bid_price;
        global Decimal commitment_request_amount;
        global Boolean compliance_consent_t_c;
        global Decimal app_secondary_investment_id;
        global integer type_of_intent;
        global integer no_of_shares_invested;
        global integer no_of_shares_wish_to_sell;
        global integer price_per_share_wish_to_sell;
        global Decimal total_invested_amount;
        global String salesforce_investment_id;
    }
    
    global class requestWrapper{
        global List<SecondaryModuleRequestWrapper> dataList;
    }
    global class responseWrapper{
        global boolean status;
        global string message;
    }
}