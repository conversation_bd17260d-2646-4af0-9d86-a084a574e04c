public class secondaryModuleTriggerHandler {
    
    public Void beforeInsert(List<Secondary_Module__c> reqList)
    {
        decimal latestIssuePrice = 0;
        List<Secondary_Module__c> secReqToUpdate = New List<Secondary_Module__c>();
        Set<Id> accIdSet = new Set<Id>();
        Set<Id> startupIdSet = new Set<Id>();
        Set<Id> investorIdSet = new Set<Id>();
        
        for (Secondary_Module__c sm : reqList) 
        {
            System.debug('Secondary_Module__c>>>>>>>>>>>>>>>>>' + sm.Name);
            /*
            latestIssuePrice = findLatestRoundIssuePrice(sm.Startup__c);
            if (latestIssuePrice != null) {
                sm.Issue_Price_of_Last_Round__c = latestIssuePrice;
            }
            */
            Account memberAcc = [SELECT Id , Name ,  Relationship_Manager__c FROM Account WHERE Id =: sm.Member_Name__c ];
            sm.Relationship_Manager__c = memberAcc.Relationship_Manager__c;
            
            Startup_Round__c parentRound = [SELECT Id , HQ_Mentor__c FROM Startup_round__c WHERE Id =: sm.Startup_Round__c];
            sm.HQ_Mentor__c = parentRound.HQ_Mentor__c;
            
            if(sm.Status__c == null){
                sm.Status__c = 'Request Submitted';
            }
            
            if(sm.Type_of_Intent__c == 'Selling Intent' && sm.Investor_Name__c == null){
                Investment__c invRecord = [select Id , Name , Investor__c FROM Investment__c WHERE Id =: sm.Investment__c LIMIT 1 ];
                sm.Investor_Name__c = invRecord.Investor__c;
            }
        }
    }
    
    public Void afterInsert(List<Secondary_Module__c> reqList)
    {
        List<Secondary_Module__c> secReqToUpdate = new List<Secondary_Module__c>();
        Set<Id> startupIdSet = new Set<Id>();
        List<Secondary_Module__c> childReqList = new List<Secondary_Module__c>();
        Map<Id , Decimal> strBuyAmountMap = new Map<Id , Decimal>();
        Map<Id , Decimal> strSellAmountMap = new Map<Id , Decimal>();
        Map<Id , Decimal> buyBlncAmountMap = New Map<Id , Decimal>();
        Map<Id , Decimal> sellBlncAmountMap = New Map<Id , Decimal>();
        Decimal Amount;
        
        for(Secondary_Module__c sm : reqList) 
        {
            Secondary_Module__c smToUpdate = new Secondary_Module__c();
            smToUpdate.Id = sm.Id;
            System.debug('smToUpdate>>>>>>>>>>>>>>>>>' + smToUpdate);
            System.debug('Reserve_Price__c>>>>>>>>>>>>>>>>>' + sm.Reserve_Price__c);
            
            if(sm.Bid_Price__c == sm.Reserve_Price__c){
                smToUpdate.Member_Bid_vs_Reserve_Price__c = 'Same';
            }else if(sm.Bid_Price__c > sm.Reserve_Price__c){
                smToUpdate.Member_Bid_vs_Reserve_Price__c = 'Member Bid More than Reserve Price';
            }else if(sm.Bid_Price__c < sm.Reserve_Price__c){
                smToUpdate.Member_Bid_vs_Reserve_Price__c = 'Member Bid Less than Reserve Price';
            }
            smToUpdate.Initial_Reserve_Price__c = sm.Reserve_Price__c;
            secReqToUpdate.add(smToUpdate);
            //Added by bharat for selling intent requirement on 29-07-2025
            startupIdSet.add(sm.Startup__c);
        }
        
        //Added by bharat for selling intent requirement on 28-07-2025
        childReqList = [Select Id , Status__c , Startup__c  , Type_of_Intent__c , Commitment_Request_Amount__c, Number_of_shares_wish_to_sell__c, Price_per_share_wish_to_sell_at__c FROM Secondary_Module__c WHERE Startup__c in : startupIdSet  ];
        for ( Secondary_Module__c sm : childReqList) 
        {
            
            /*           
            if(sm.Type_of_Intent__c == 'Buying Intent' && (sm.Status__c == 'Transaction Done' || sm.Status__c == 'Rejected'))
            {
                Amount = sm.Commitment_Request_Amount__c;
                if(buyBlncAmountMap.containsKey(sm.Startup__c))
                {
                    decimal totalAmount = buyBlncAmountMap.get(sm.Startup__c) + Amount;
                    buyBlncAmountMap.put(sm.Startup__c ,totalAmount);
                }else
                {
                    buyBlncAmountMap.put(sm.Startup__c , Amount);
                }
            }else if(sm.Type_of_Intent__c == 'Selling Intent' && (sm.Status__c == 'Transaction Done' || sm.Status__c == 'Rejected'))
            {
                Amount = sm.Commitment_Request_Amount__c;
                if(sellBlncAmountMap.containsKey(sm.Startup__c))
                {
                    decimal totalAmount = sellBlncAmountMap.get(sm.Startup__c) + Amount;
                    sellBlncAmountMap.put(sm.Startup__c ,totalAmount);
                }else
                {
                    sellBlncAmountMap.put(sm.Startup__c , Amount);
                }
            }
            */
            if(sm.Type_of_Intent__c == 'Buying Intent')
            {
                Amount = sm.Commitment_Request_Amount__c;
                if(strBuyAmountMap.containsKey(sm.Startup__c))
                {
                    Decimal totalAmount = strBuyAmountMap.get(sm.Startup__c) + Amount;
                    strBuyAmountMap.put(sm.Startup__c , totalAmount);
                }else
                {
                    strBuyAmountMap.put(sm.Startup__c , Amount);
                }
                
                if(sm.Status__c == 'Transaction Done' || sm.Status__c == 'Rejected'){
                    Amount = sm.Commitment_Request_Amount__c;
                    if(buyBlncAmountMap.containsKey(sm.Startup__c))
                    {
                        decimal totalAmount = buyBlncAmountMap.get(sm.Startup__c) + Amount;
                        buyBlncAmountMap.put(sm.Startup__c ,totalAmount);
                    }else
                    {
                        buyBlncAmountMap.put(sm.Startup__c , Amount);
                    }
                }
            }else if(sm.Type_of_Intent__c == 'Selling Intent')
            {
                Amount = sm.Number_of_shares_wish_to_sell__c * sm.Price_per_share_wish_to_sell_at__c;
                if(strSellAmountMap.containsKey(sm.Startup__c))
                {
                    Decimal totalAmount = strSellAmountMap.get(sm.Startup__c) + Amount ;
                    strSellAmountMap.put(sm.Startup__c, totalAmount);
                }else
                {
                    strSellAmountMap.put(sm.Startup__c , Amount);
                }
                
                If(sm.Status__c == 'Transaction Done' || sm.Status__c == 'Rejected')
                {
                    if(sellBlncAmountMap.containsKey(sm.Startup__c))
                    {
                        decimal totalAmount = sellBlncAmountMap.get(sm.Startup__c) + Amount;
                        sellBlncAmountMap.put(sm.Startup__c ,totalAmount);
                    }else
                    {
                        sellBlncAmountMap.put(sm.Startup__c , Amount);
                    }
                }
            }
        }
        
        System.debug('strBuyAmountMap>>>>>>>>>>' + strBuyAmountMap);
        System.debug('strSellAmountMap>>>>>>>>>>' + strSellAmountMap);
        System.debug('buyBlncAmountMap>>>>>>>>>>' + buyBlncAmountMap);
        System.debug('sellBlncAmountMap>>>>>>>>>>' + sellBlncAmountMap);
        System.debug('buyBlncAmountMap is nulll >>>>>>>>>>' + buyBlncAmountMap.isEmpty() );
        
        List<Startup__c> strToUpdate = [Select Id ,Total_amount_Rec_Buying_Intent__c , Total_amount_Rec_Selling_Intent__c , Balance_secondary_Buying__c , Balance_secondary_selling__c From Startup__c WHERE Id IN : startupIdSet ];
        if(strToUpdate != null && strToUpdate.size()>0 )
        {
            For(Startup__c str : strToUpdate){
                if(!(strBuyAmountMap.isEmpty()) && (strBuyAmountMap.get(str.Id) != null)){
                    str.Total_amount_Rec_Buying_Intent__c = strBuyAmountMap.get(str.Id);
                    if(strBuyAmountMap.get(str.Id) >= 1000000){
                        str.Applicable_for_Secondary_Selling__c = TRUE;
                    }else{
                        str.Applicable_for_Secondary_Selling__c = FALSE;
                    }
                     If(!(buyBlncAmountMap.isEmpty()) && (buyBlncAmountMap.get(str.Id) != null)){
                         str.Balance_secondary_Buying__c = strBuyAmountMap.get(str.Id) - buyBlncAmountMap.get(str.Id);
                     }else{
                         str.Balance_secondary_Buying__c = strBuyAmountMap.get(str.Id);
                     }
                    
                }
                If(!(strSellAmountMap.isEmpty()) && (strSellAmountMap.get(str.Id) != null)){
                    str.Total_amount_Rec_Selling_Intent__c = strSellAmountMap.get(str.Id);
                    If(!(sellBlncAmountMap.isEmpty()) && (sellBlncAmountMap.get(str.Id) != null)){
                         str.Balance_secondary_selling__c = strSellAmountMap.get(str.Id) - sellBlncAmountMap.get(str.Id);
                     }else{
                         str.Balance_secondary_selling__c = strSellAmountMap.get(str.Id);
                     }
                }
                
            }
            
            update strToUpdate;
        }
        if(secReqToUpdate != null && secReqToUpdate.size()>0){
            update secReqToUpdate;
        }
    }

    public Void afterUpdate(List<Secondary_Module__c> triggerNew,Map<id,Secondary_Module__c> triggerOldMap)
    {
        Set<Id> startupIdSet = new Set<Id>();
        List<Secondary_Module__c> childReqList = new List<Secondary_Module__c>();
        Map<Id , Decimal> strBuyAmountMap = new Map<Id , Decimal>();
        Map<Id , Decimal> strSellAmountMap = new Map<Id , Decimal>();
        Map<Id , Decimal> buyBlncAmountMap = New Map<Id , Decimal>();
        Map<Id , Decimal> sellBlncAmountMap = New Map<Id , Decimal>();
        
        for(Secondary_Module__c sm : triggerNew){
            startupIdSet.add(sm.Startup__c);
        }
        
        childReqList = [Select Id , Status__c , Startup__c  , Type_of_Intent__c , Commitment_Request_Amount__c, Number_of_shares_wish_to_sell__c, Price_per_share_wish_to_sell_at__c FROM Secondary_Module__c WHERE Startup__c in : startupIdSet];
        for ( Secondary_Module__c sm : childReqList) 
        {
            Decimal Amount;
            if(sm.Type_of_Intent__c == 'Buying Intent')
            {
                Amount = sm.Commitment_Request_Amount__c;
                if(strBuyAmountMap.containsKey(sm.Startup__c))
                {
                    Decimal totalAmount = strBuyAmountMap.get(sm.Startup__c) + Amount;
                    strBuyAmountMap.put(sm.Startup__c , totalAmount);
                }else
                {
                    strBuyAmountMap.put(sm.Startup__c , Amount);
                }
                if(sm.Status__c == 'Transaction Done' || sm.Status__c == 'Rejected'){
                    Amount = sm.Commitment_Request_Amount__c;
                    if(buyBlncAmountMap.containsKey(sm.Startup__c))
                    {
                        decimal totalAmount = buyBlncAmountMap.get(sm.Startup__c) + Amount;
                        buyBlncAmountMap.put(sm.Startup__c ,totalAmount);
                    }else
                    {
                        buyBlncAmountMap.put(sm.Startup__c , Amount);
                    }
                }
            }else if(sm.Type_of_Intent__c == 'Selling Intent')
            {
                Amount = sm.Number_of_shares_wish_to_sell__c * sm.Price_per_share_wish_to_sell_at__c;
                If(strSellAmountMap.containsKey(sm.Startup__c))
                {
                    Decimal totalAmount = strSellAmountMap.get(sm.Startup__c) + Amount ;
                    strSellAmountMap.put(sm.Startup__c, totalAmount);
                }else
                {
                    strSellAmountMap.put(sm.Startup__c , Amount);
                }
                
                If(sm.Status__c == 'Transaction Done' || sm.Status__c == 'Rejected')
                {
                    if(sellBlncAmountMap.containsKey(sm.Startup__c))
                    {
                        decimal totalAmount = sellBlncAmountMap.get(sm.Startup__c) + Amount;
                        sellBlncAmountMap.put(sm.Startup__c ,totalAmount);
                    }else
                    {
                        sellBlncAmountMap.put(sm.Startup__c , Amount);
                    }
                }
            }
        }
        
        System.debug('buyBlncAmountMap>>>>>>>>>>' + buyBlncAmountMap);
        System.debug('sellBlncAmountMap>>>>>>>>>>' + sellBlncAmountMap);
        System.debug('buyBlncAmountMap is nulll >>>>>>>>>>' + buyBlncAmountMap.isEmpty() );
        List<Startup__c> strToUpdate = [Select Id ,Total_amount_Rec_Buying_Intent__c , Total_amount_Rec_Selling_Intent__c , Balance_secondary_Buying__c , Balance_secondary_selling__c From Startup__c WHERE Id IN : startupIdSet ];
        if(strToUpdate != null && strToUpdate.size()>0 )
        {
            For(Startup__c str : strToUpdate){
                System.debug('str>>>>>>>>>>' + str);
                System.debug('str.Balance_secondary_Buying__c>>>>>>>>>>' + str.Balance_secondary_Buying__c);
                System.debug('buyBlncAmountMap.get(str.Id)>>>>>>>>>>' + buyBlncAmountMap.get(str.Id));
                if(!(strBuyAmountMap.isEmpty()) && (strBuyAmountMap.get(str.Id) != null)){
                    str.Total_amount_Rec_Buying_Intent__c = strBuyAmountMap.get(str.Id);
                    if(strBuyAmountMap.get(str.Id) >= 1000000){
                        str.Applicable_for_Secondary_Selling__c = TRUE;
                    }else{
                        str.Applicable_for_Secondary_Selling__c = FALSE;
                    }
                     If(!(buyBlncAmountMap.isEmpty()) && (buyBlncAmountMap.get(str.Id) != null)){
                         str.Balance_secondary_Buying__c = strBuyAmountMap.get(str.Id) - buyBlncAmountMap.get(str.Id);
                     }else{
                         str.Balance_secondary_Buying__c = strBuyAmountMap.get(str.Id);
                     }
                    
                }
                If(!(strSellAmountMap.isEmpty()) && (strSellAmountMap.get(str.Id) != null)){
                    str.Total_amount_Rec_Selling_Intent__c = strSellAmountMap.get(str.Id);
                    If(!(sellBlncAmountMap.isEmpty()) && (sellBlncAmountMap.get(str.Id) != null)){
                         str.Balance_secondary_selling__c = strSellAmountMap.get(str.Id) - sellBlncAmountMap.get(str.Id);
                     }else{
                         str.Balance_secondary_selling__c = strSellAmountMap.get(str.Id);
                     }
                }
            }
            
            update strToUpdate;
        }
    }
    
    
    /*
    public decimal findLatestRoundIssuePrice(String startupId){
        
        String roundQuery = 'Select Id , Name ,Issue_Price__c , Startup__c , Round_Type__c FROM Startup_Round__c ' +
            				'where Startup__c =: startupId AND Round_Type__c = \'Raise\' ';
        
        Startup_Round__c childRoundList = [Select Id , Name ,Issue_Price__c , Startup__c , Round_Type__c FROM Startup_Round__c 
                                           where Startup__c =: startupId AND Round_Type__c = 'Raise' ORDER BY CreatedDate 
                                           DESC LIMIT 1 ];
        
        System.debug('childRoundList ID>>>>>>' + childRoundList.Id);
        System.debug('Issue Price >>>>>>' + childRoundList.Issue_Price__c);
        return childRoundList.Issue_Price__c ;
        
    }*/
}