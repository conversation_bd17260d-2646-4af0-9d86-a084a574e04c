public class ReferralTaskCreationScheduler implements Schedulable {
    public void execute(SchedulableContext sc) {
        Date sixMonthsAgo = Date.today().addMonths(-6);
        Date todayDate = Date.today();

       
        List<Account> AccLst = [
            SELECT Id, Date_of_1st_Payment__c, Relationship_Manager__c
            FROM Account
            WHERE Date_of_1st_Payment__c != null
        ];
        Set<Id> accountIds = new Set<Id>();
        for (Account acc : AccLst) {
            accountIds.add(acc.Id);
        }

        Map<Id, Set<String>> existingTaskMap = new Map<Id, Set<String>>();
        for (Task t : [
            SELECT WhatId, Referral_Trigger_Type__c
            FROM Task
            WHERE Referral_Trigger_Status__c != null
            AND WhatId IN :accountIds
            AND Subject IN ('Refferal Trigger Completed 3 Months with IPV' ,'Refferal Trigger Half yearly reminder')
        ]) {
            if (!existingTaskMap.containsKey(t.WhatId)) {
                existingTaskMap.put(t.WhatId, new Set<String>());
            }
            existingTaskMap.get(t.WhatId).add(t.Referral_Trigger_Type__c);
        }

      
        Map<Id, List<Lead__c>> accountLeadMap = new Map<Id, List<Lead__c>>();
        for (Lead__c ld : [
            SELECT Id, Referred_By__c
            FROM Lead__c
            WHERE Referred_By__c IN :accountIds
            AND CreatedDate >= :sixMonthsAgo
        ]) {
            if (!accountLeadMap.containsKey(ld.Referred_By__c)) {
                accountLeadMap.put(ld.Referred_By__c, new List<Lead__c>());
            }
            accountLeadMap.get(ld.Referred_By__c).add(ld);
        }

        Map<Id, List<Account>> accountChildMap = new Map<Id, List<Account>>();
        for (Account childAcc : [
            SELECT Id, ParentId
            FROM Account
            WHERE ParentId IN :accountIds
            AND CreatedDate >= :sixMonthsAgo
        ]) {
            if (!accountChildMap.containsKey(childAcc.ParentId)) {
                accountChildMap.put(childAcc.ParentId, new List<Account>());
            }
            accountChildMap.get(childAcc.ParentId).add(childAcc);
        }

        List<Task> TaskLst = new List<Task>();

        for (Account acc : AccLst) {
            Date threeMonthCompletionDate = acc.Date_of_1st_Payment__c.addDays(90);
            Integer monthsSince = acc.Date_of_1st_Payment__c.monthsBetween(todayDate);
            Set<String> existingTriggers = existingTaskMap.get(acc.Id);

           
            if (threeMonthCompletionDate == todayDate &&
                (existingTriggers == null || !existingTriggers.contains('3 Month Milestone'))) {
                Task tsk = new Task();
                tsk.Referral_Trigger_Status__c = 'Pending';
                tsk.Referral_Trigger_Type__c = '3 Month Milestone';
                tsk.Subject = 'Refferal Trigger Completed 3 Months with IPV';
                tsk.OwnerId = acc.Relationship_Manager__c;
                tsk.WhatId = acc.Id;
                tsk.ActivityDate = todayDate.addDays(1);
                TaskLst.add(tsk);
            }

            
            Boolean hasLeads = accountLeadMap.containsKey(acc.Id) && !accountLeadMap.get(acc.Id).isEmpty();
            Boolean hasAccounts = accountChildMap.containsKey(acc.Id) && !accountChildMap.get(acc.Id).isEmpty();
            if (!hasLeads && !hasAccounts) {
                if (Math.mod(monthsSince, 6) == 0 &&
                    (existingTriggers == null || !existingTriggers.contains('Nudge for referrals every 6 months with IPV'))) {
                    Task tsk = new Task();
                    tsk.Referral_Trigger_Status__c = 'Pending';
                    tsk.Referral_Trigger_Type__c = 'Nudge for referrals every 6 months with IPV';
                    tsk.Subject = 'Refferal Trigger Half yearly reminder';
                    tsk.OwnerId = acc.Relationship_Manager__c;
                    tsk.WhatId = acc.Id;
                    tsk.ActivityDate = todayDate.addDays(1);
                    TaskLst.add(tsk);
                }
            }
        }

        
        Date activityDate = todayDate.addDays(-1);
        List<User_Activity__c> MemberLst = [
            SELECT Related_Account__c, Related_Account__r.Relationship_Manager__c,
                   Related_Lead__c, Related_Lead__r.Relationship_Manager__c
            FROM User_Activity__c
            WHERE Activity_Type__c = 'Refer a Member'
            AND Time_Stamp__c = :activityDate
        ];

        Set<Id> relatedIds = new Set<Id>();
        for (User_Activity__c act : MemberLst) {
            if (act.Related_Account__c != null) {
                relatedIds.add(act.Related_Account__c);
            }
            if (act.Related_Lead__c != null) {
                relatedIds.add(act.Related_Lead__c);
            }
        }

        
        Map<Id, Set<String>> relatedReferralTasks = new Map<Id, Set<String>>();
        for (Task t : [
            SELECT WhatId, Referral_Trigger_Type__c
            FROM Task
            WHERE WhatId IN :relatedIds
            AND Referral_Trigger_Status__c != null
            AND Subject = 'Refferal Trigger Referred a Member via App'
        ]) {
            if (!relatedReferralTasks.containsKey(t.WhatId)) {
                relatedReferralTasks.put(t.WhatId, new Set<String>());
            }
            relatedReferralTasks.get(t.WhatId).add(t.Referral_Trigger_Type__c);
        }

      
        for (User_Activity__c m : MemberLst) {
            if (m.Related_Account__c != null) {
                Set<String> triggers = relatedReferralTasks.get(m.Related_Account__c);
                if (triggers == null || !triggers.contains('Refer a member via App')) {
                    Task tsk = new Task();
                    tsk.Referral_Trigger_Status__c = 'Pending';
                    tsk.Referral_Trigger_Type__c = 'Refer a member via App';
                    tsk.Subject = 'Refferal Trigger Referred a Member via App';
                    tsk.OwnerId = m.Related_Account__r.Relationship_Manager__c;
                    tsk.WhatId = m.Related_Account__c;
                    tsk.ActivityDate = todayDate.addDays(1);
                    TaskLst.add(tsk);
                }
            }
            if (m.Related_Lead__c != null) {
                Set<String> triggers = relatedReferralTasks.get(m.Related_Lead__c);
                if (triggers == null || !triggers.contains('Refer a member via App')) {
                    Task tsk = new Task();
                    tsk.Referral_Trigger_Status__c = 'Pending';
                    tsk.Referral_Trigger_Type__c = 'Refer a member via App';
                    tsk.Subject = 'Refferal Trigger Referred a Member via App';
                    tsk.OwnerId = m.Related_Lead__r.Relationship_Manager__c;
                    tsk.WhatId = m.Related_Lead__c;
                    tsk.ActivityDate = todayDate.addDays(1);
                    TaskLst.add(tsk);
                }
            }
        }

       
        if (!TaskLst.isEmpty()) {
            insert TaskLst;
        }

        
        List<Task> overdueTasks = [
            SELECT Id, Status, ActivityDate, OwnerId, WhatId, Subject, Referral_Trigger_Status__c
            FROM Task
            WHERE Referral_Trigger_Status__c = 'Pending'
            AND ActivityDate < :todayDate
        ];

        List<Task> tasksToUpdate = new List<Task>();
        List<Messaging.SingleEmailMessage> emailsToSend = new List<Messaging.SingleEmailMessage>();

       
        Set<Id> ownerIds = new Set<Id>();
        Set<Id> whatIds = new Set<Id>();
        for (Task t : overdueTasks) {
            ownerIds.add(t.OwnerId);
            whatIds.add(t.WhatId);
        }

        Map<Id, User> userMap = new Map<Id, User>([
            SELECT Id, Name, Email
            FROM User
            WHERE Id IN :ownerIds
        ]);

        Map<Id, Account> accountMap = new Map<Id, Account>([
            SELECT Id, Name
            FROM Account
            WHERE Id IN :whatIds
        ]);

        for (Task t : overdueTasks) {
            t.Referral_Trigger_Status__c = 'Overdue';
            t.ActivityDate = todayDate.addDays(2);
            tasksToUpdate.add(t);

            User rm = userMap.get(t.OwnerId);
            Account member = accountMap.get(t.WhatId);

            if (rm != null && member != null) {
                Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
                mail.setToAddresses(new String[] { rm.Email });
                mail.setSubject('Action Required: Referral Trigger Notification – ' + member.Name);
                String body = 'Dear ' + rm.Name + ',\n\n' +
                    'We hope this message finds you well.\n\n' +
                    'Your member, ' + member.Name + ', has recently triggered a referral milestone. This presents a timely opportunity to engage with them and encourage potential referrals to the IPV network.\n\n' +
                    'We request you to kindly reach out to the member and offer any necessary support or guidance.\n\n' +
                    'Thank you for your continued efforts.\n\n' +
                    'Warm regards,\n' +
                    'Team IPV';
                mail.setPlainTextBody(body);
                emailsToSend.add(mail);
            }
        }

        if (!tasksToUpdate.isEmpty()) {
            update tasksToUpdate;
            System.Debug('TAsk UPdated Succesfuuly>>>'+tasksToUpdate);
        }

        if (!emailsToSend.isEmpty()) {
            Messaging.sendEmail(emailsToSend);
            System.Debug('MAil Send successfully>>>>'+emailsToSend);
        }
    }
}